{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"outDir": "dist", "rootDir": "src", "jsx": "react-jsx", "lib": ["DOM", "ESNext"], "module": "ESNext", "isolatedModules": true, "target": "ES2020", "moduleResolution": "bundler", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "exclude": ["node_modules", "dist"], "references": [{"path": "../../config/eslint-config/tsconfig.build.json"}, {"path": "../../config/postcss-config/tsconfig.build.json"}, {"path": "../../config/rsbuild-config/tsconfig.build.json"}, {"path": "../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../config/tailwind-config/tsconfig.build.json"}, {"path": "../../config/ts-config/tsconfig.build.json"}, {"path": "../../config/vitest-config/tsconfig.build.json"}, {"path": "../../infra/plugins/import-watch-loader/tsconfig.build.json"}, {"path": "../../infra/plugins/pkg-root-webpack-plugin/tsconfig.build.json"}, {"path": "../../packages/agent-ide/agent-publish/tsconfig.build.json"}, {"path": "../../packages/agent-ide/entry-adapter/tsconfig.build.json"}, {"path": "../../packages/agent-ide/layout-adapter/tsconfig.build.json"}, {"path": "../../packages/arch/api-schema/tsconfig.build.json"}, {"path": "../../packages/arch/bot-env/tsconfig.build.json"}, {"path": "../../packages/arch/bot-flags/tsconfig.build.json"}, {"path": "../../packages/arch/bot-md-box-adapter/tsconfig.build.json"}, {"path": "../../packages/arch/bot-typings/tsconfig.build.json"}, {"path": "../../packages/arch/default-slardar/tsconfig.build.json"}, {"path": "../../packages/arch/foundation-sdk/tsconfig.build.json"}, {"path": "../../packages/arch/i18n/tsconfig.build.json"}, {"path": "../../packages/arch/logger/tsconfig.build.json"}, {"path": "../../packages/arch/responsive-kit/tsconfig.build.json"}, {"path": "../../packages/arch/web-context/tsconfig.build.json"}, {"path": "../../packages/community/explore/tsconfig.build.json"}, {"path": "../../packages/foundation/account-adapter/tsconfig.build.json"}, {"path": "../../packages/foundation/account-ui-adapter/tsconfig.build.json"}, {"path": "../../packages/foundation/foundation-sdk/tsconfig.build.json"}, {"path": "../../packages/foundation/global-adapter/tsconfig.build.json"}, {"path": "../../packages/foundation/global/tsconfig.build.json"}, {"path": "../../packages/foundation/layout/tsconfig.build.json"}, {"path": "../../packages/foundation/space-ui-adapter/tsconfig.build.json"}, {"path": "../../packages/foundation/space-ui-base/tsconfig.build.json"}, {"path": "../../packages/project-ide/main/tsconfig.build.json"}, {"path": "../../packages/studio/stores/bot-plugin/tsconfig.build.json"}, {"path": "../../packages/studio/workspace/entry-adapter/tsconfig.build.json"}, {"path": "../../packages/studio/workspace/entry-base/tsconfig.build.json"}, {"path": "../../packages/studio/workspace/project-publish/tsconfig.build.json"}, {"path": "../../packages/workflow/adapter/playground/tsconfig.build.json"}]}