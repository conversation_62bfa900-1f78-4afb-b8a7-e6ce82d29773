{"name": "@coze-studio/app", "version": "0.0.1", "private": true, "description": "coze studio app", "keywords": [], "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "sideEffects": false, "scripts": {"build": "IS_OPEN_SOURCE=true rsbuild build", "dev": "IS_OPEN_SOURCE=true CUSTOM_VERSION=release rsbuild dev", "lint": "eslint ./ --cache --quiet", "preview": "rsbuild preview", "test": "vitest --run --passWithNoTests", "test:cov": "vitest --run --passWithNoTests --coverage"}, "dependencies": {"@coze-agent-ide/agent-publish": "workspace:*", "@coze-agent-ide/entry-adapter": "workspace:*", "@coze-agent-ide/layout-adapter": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-md-box-adapter": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/foundation-sdk": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/web-context": "workspace:*", "@coze-foundation/account-adapter": "workspace:*", "@coze-foundation/account-ui-adapter": "workspace:*", "@coze-foundation/foundation-sdk": "workspace:*", "@coze-foundation/global": "workspace:*", "@coze-foundation/global-adapter": "workspace:*", "@coze-foundation/layout": "workspace:*", "@coze-foundation/space-ui-adapter": "workspace:*", "@coze-foundation/space-ui-base": "workspace:*", "@coze-project-ide/main": "workspace:*", "@coze-studio/api-schema": "workspace:*", "@coze-studio/default-slardar": "workspace:*", "@coze-studio/project-publish": "workspace:*", "@coze-studio/workspace-adapter": "workspace:*", "@coze-studio/workspace-base": "workspace:*", "@coze-workflow/playground-adapter": "workspace:*", "path-browserify": "^1.0.1", "react": "~18.2.0", "react-dom": "~18.2.0", "react-error-boundary": "^4.0.9", "react-router-dom": "^6.11.1", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/bot-env": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/import-watch-loader": "workspace:*", "@coze-arch/pkg-root-webpack-plugin": "workspace:*", "@coze-arch/postcss-config": "workspace:*", "@coze-arch/responsive-kit": "workspace:*", "@coze-arch/rsbuild-config": "workspace:*", "@coze-arch/semi-theme-hand01": "0.0.6-alpha.346d77", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/tailwind-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@coze-community/explore": "workspace:*", "@coze-studio/bot-plugin-store": "workspace:*", "@rsbuild/core": "~1.1.0", "@rsdoctor/rspack-plugin": "1.0.0-rc.0", "@rspack/core": ">=0.7", "@types/node": "18.18.9", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "sucrase": "^3.32.0", "tailwindcss": "~3.3.3", "typescript": "~5.8.2", "vitest": "~3.0.5", "webpack": "~5.91.0"}}