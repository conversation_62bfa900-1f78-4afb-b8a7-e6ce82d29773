{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"rootDir": "./", "outDir": "./dist", "jsx": "react-jsx", "lib": ["DOM", "ESNext"], "module": "ESNext", "target": "ES2020", "moduleResolution": "bundler", "types": ["vitest/globals", "node"]}, "include": ["__tests__", "vitest.config.ts", "tailwind.config.ts", "rsbuild.config.ts", "scripts/**/*.ts"], "exclude": ["./dist"], "references": [{"path": "./tsconfig.build.json"}]}