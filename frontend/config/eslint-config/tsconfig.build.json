{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.node.json", "compilerOptions": {"sourceMap": false, "lib": ["ES2021"], "esModuleInterop": true, "types": ["node"], "rootDir": "./src", "outDir": "dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["./src", "./src/**/*.json"], "references": [{"path": "../../infra/eslint-plugin/tsconfig.build.json"}, {"path": "../ts-config/tsconfig.build.json"}]}