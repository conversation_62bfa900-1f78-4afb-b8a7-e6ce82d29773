# @coze-arch/rsbuild-config

rsbuild config

## Overview

This package is part of the Coze Studio monorepo and provides ui component functionality. It includes plugin.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-arch/rsbuild-config": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-arch/rsbuild-config';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Plugin

## API Reference

Please refer to the TypeScript definitions for detailed API documentation.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
