{"name": "@coze-arch/rsbuild-config", "version": "0.0.1", "description": "rsbuild config ", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-env": "workspace:*", "@coze-arch/pkg-root-webpack-plugin": "workspace:*", "@coze-arch/semi-theme-hand01": "0.0.6-alpha.346d77", "@coze-common/assets": "workspace:*", "@douyinfe/semi-rspack-plugin": "2.61.0", "@rsbuild/core": "~1.1.0", "@rsbuild/plugin-less": "~1.1.0", "@rsbuild/plugin-react": "~1.1.0", "@rsbuild/plugin-sass": "~1.1.0", "@rsbuild/plugin-svgr": "~1.0.6", "postcss": "^8.4.32", "postcss-nesting": "^12.1.0", "tailwindcss": "~3.3.3"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@types/node": "^18", "@vitest/coverage-v8": "~3.0.5", "sucrase": "^3.32.0", "typescript": "~5.8.2", "vitest": "~3.0.5", "webpack": "~5.91.0"}}