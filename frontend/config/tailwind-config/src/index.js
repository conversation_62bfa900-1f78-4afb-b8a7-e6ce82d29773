module.exports = {
  darkMode: 'class',
  prefix: '',
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        foreground: {
          DEFAULT: 'rgba(var(--foreground), 1)',
          revert: 'rgba(var(--coze-fg-revert), var(--coze-fg-revert-alpha))',
          white: 'rgba(var(--coze-fg-white), var(--coze-fg-white-alpha))',
          7: 'rgba(var(--coze-fg-7), 1)',
          6: 'rgba(var(--coze-fg-6), 1)',
          5: 'rgba(var(--coze-fg-5), var(--coze-fg-5-alpha))',
          4: 'rgba(var(--coze-fg-4), var(--coze-fg-4-alpha))',
          3: 'rgba(var(--coze-fg-3), var(--coze-fg-3-alpha))',
          2: 'rgba(var(--coze-fg-2), var(--coze-fg-2-alpha))',
          1: 'rgba(var(--coze-fg-1), var(--coze-fg-1-alpha))',
        },
        background: {
          DEFAULT: 'rgba(var(--background), 1)',
          9: 'rgba(var(--coze-bg-9), var(--coze-bg-9-alpha))',
          8: 'rgba(var(--coze-bg-8), var(--coze-bg-8-alpha))',
          7: 'rgba(var(--coze-bg-7), var(--coze-bg-7-alpha))',
          6: 'rgba(var(--coze-bg-6), var(--coze-bg-6-alpha))',
          5: 'rgba(var(--coze-bg-5), var(--coze-bg-5-alpha))',
          4: 'rgba(var(--coze-bg-4), var(--coze-bg-4-alpha))',
          3: 'rgba(var(--coze-bg-3), var(--coze-bg-3-alpha))',
          2: 'rgba(var(--coze-bg-2), var(--coze-bg-2-alpha))',
          1: 'rgba(var(--coze-bg-1), var(--coze-bg-1-alpha))',
          0: 'rgba(var(--coze-bg-0), 1)',
        },
        brand: {
          DEFAULT: 'rgba(var(--coze-brand-7), 1)',
          linear: 'rgba(var(--coze-brand-5), 0)',
          50: 'rgba(var(--coze-brand-50), var(--coze-brand-50-alpha))',
          30: 'rgba(var(--coze-brand-30), var(--coze-brand-30-alpha))',
          7: 'rgba(var(--coze-brand-7), 1)',
          6: 'rgba(var(--coze-brand-6), 1)',
          5: 'rgba(var(--coze-brand-5), 1)',
          3: 'rgba(var(--coze-brand-3), var(--coze-brand-3-alpha))',
          2: 'rgba(var(--coze-brand-2), var(--coze-brand-2-alpha))',
          1: 'rgba(var(--coze-brand-1), var(--coze-brand-1-alpha))',
          0: 'rgba(var(--coze-brand-0), var(--coze-brand-0-alpha))',
        },
        red: {
          DEFAULT: 'rgba(var(--coze-red-7), 1)',
          linear: 'rgba(var(--coze-red-5), 0)',
          7: 'rgba(var(--coze-red-7), 1)',
          6: 'rgba(var(--coze-red-6), 1)',
          5: 'rgba(var(--coze-red-5), 1)',
          3: 'rgba(var(--coze-red-3), var(--coze-red-3-alpha))',
          2: 'rgba(var(--coze-red-2), var(--coze-red-2-alpha))',
          1: 'rgba(var(--coze-red-1), var(--coze-red-1-alpha))',
          0: 'rgba(var(--coze-red-0), var(--coze-red-0-alpha))',
        },
        yellow: {
          DEFAULT: 'rgba(var(--coze-yellow-7), 1)',
          linear: 'rgba(var(--coze-yellow-5), 0)',
          50: 'rgba(var(--coze-yellow-50), var(--coze-yellow-50-alpha))',
          30: 'rgba(var(--coze-yellow-30), var(--coze-yellow-30-alpha))',
          7: 'rgba(var(--coze-yellow-7), 1)',
          6: 'rgba(var(--coze-yellow-6), 1)',
          5: 'rgba(var(--coze-yellow-5), 1)',
          3: 'rgba(var(--coze-yellow-3), var(--coze-yellow-3-alpha))',
          2: 'rgba(var(--coze-yellow-2), var(--coze-yellow-2-alpha))',
          1: 'rgba(var(--coze-yellow-1), var(--coze-yellow-1-alpha))',
          0: 'rgba(var(--coze-yellow-0), var(--coze-yellow-0-alpha))',
        },
        green: {
          DEFAULT: 'rgba(var(--coze-green-7), 1)',
          linear: 'rgba(var(--coze-green-5), 0)',
          7: 'rgba(var(--coze-green-7), 1)',
          6: 'rgba(var(--coze-green-6), 1)',
          5: 'rgba(var(--coze-green-5), 1)',
          3: 'rgba(var(--coze-green-3), var(--coze-green-3-alpha))',
          2: 'rgba(var(--coze-green-2), var(--coze-green-2-alpha))',
          1: 'rgba(var(--coze-green-1), var(--coze-green-1-alpha))',
          0: 'rgba(var(--coze-green-0), var(--coze-green-0-alpha))',
        },
        emerald: {
          DEFAULT: 'rgba(var(--coze-emerald-5), 1)',
          linear: 'rgba(var(--coze-emerald-5), 0)',
          50: 'rgba(var(--coze-emerald-50), var(--coze-emerald-50-alpha))',
          30: 'rgba(var(--coze-emerald-30), var(--coze-emerald-30-alpha))',
          20: 'rgba(var(--coze-emerald-20), 1)',
          10: 'rgba(var(--coze-emerald-10), 1)',
          5: 'rgba(var(--coze-emerald-5), 1)',
          3: 'rgba(var(--coze-emerald-3), var(--coze-emerald-3-alpha))',
        },
        orange: {
          DEFAULT: 'rgba(var(--coze-orange-5), 1)',
          linear: 'rgba(var(--coze-orange-5), 0)',
          5: 'rgba(var(--coze-orange-5), 1)',
          3: 'rgba(var(--coze-orange-3), var(--coze-orange-3-alpha))',
          1: 'rgba(var(--coze-orange-1), var(--coze-orange-1-alpha))',
        },
        alternative: {
          DEFAULT: 'rgba(var(--coze-alternative-50), 1)',
          50: 'rgba(var(--coze-alternative-50), 1)',
          30: 'rgba(var(--coze-alternative-30), var(--coze-alternative-30-alpha))',
        },
        cyan: {
          DEFAULT: 'rgba(var(--coze-cyan-5), 1)',
          linear: 'rgba(var(--coze-cyan-5), 0)',
          50: 'rgba(var(--coze-cyan-50), var(--coze-cyan-50-alpha))',
          30: 'rgba(var(--coze-cyan-30), var(--coze-cyan-30-alpha))',
          20: 'rgba(var(--coze-cyan-20), var(--coze-cyan-20-alpha))',
          10: 'rgba(var(--coze-cyan-10), var(--coze-cyan-10-alpha))',
          5: 'rgba(var(--coze-cyan-5), 1)',
          3: 'rgba(var(--coze-cyan-3), var(--coze-cyan-3-alpha))',
        },
        blue: {
          DEFAULT: 'rgba(var(--coze-blue-5), 1)',
          linear: 'rgba(var(--coze-blue-5), 0)',
          50: 'rgba(var(--coze-blue-50), var(--coze-blue-50-alpha))',
          30: 'rgba(var(--coze-blue-30), var(--coze-blue-30-alpha))',
          20: 'rgba(var(--coze-blue-20), var(--coze-blue-20-alpha))',
          10: 'rgba(var(--coze-blue-10), var(--coze-blue-10-alpha))',
          5: 'rgba(var(--coze-blue-5), var(--coze-blue-5-alpha))',
          3: 'rgba(var(--coze-blue-3), var(--coze-blue-3-alpha))',
        },
        purple: {
          DEFAULT: 'rgba(var(--coze-purple-5), 1)',
          linear: 'rgba(var(--coze-purple-5), 0)',
          50: 'rgba(var(--coze-purple-50), var(--coze-purple-50-alpha))',
          30: 'rgba(var(--coze-purple-30), var(--coze-purple-30-alpha))',
          20: 'rgba(var(--coze-purple-20), var(--coze-purple-20-alpha))',
          10: 'rgba(var(--coze-purple-10), var(--coze-purple-10-alpha))',
          7: 'rgba(var(--coze-purple-7), 1)',
          6: 'rgba(var(--coze-purple-6), 1)',
          5: 'rgba(var(--coze-purple-5), 1)',
          3: 'rgba(var(--coze-purple-3), var(--coze-purple-3-alpha))',
          2: 'rgba(var(--coze-purple-2), var(--coze-purple-2-alpha))',
          1: 'rgba(var(--coze-purple-1), var(--coze-purple-1-alpha))',
        },
        magenta: {
          DEFAULT: 'rgba(var(--coze-magenta-5), 1)',
          linear: 'rgba(var(--coze-magenta-5), 0)',
          50: 'rgba(var(--coze-magenta-50), var(--coze-magenta-50-alpha))',
          30: 'rgba(var(--coze-magenta-30), var(--coze-magenta-30-alpha))',
          20: 'rgba(var(--coze-magenta-20), var(--coze-magenta-20-alpha))',
          10: 'rgba(var(--coze-magenta-10), var(--coze-magenta-10-alpha))',
          5: 'rgba(var(--coze-magenta-5), 1)',
          3: 'rgba(var(--coze-magenta-3), var(--coze-magenta-3-alpha))',
        },
        black: {
          DEFAULT: 'rgb(var(--black-6))',
          7: 'rgb(var(--black-7))',
          6: 'rgb(var(--black-6))',
          5: 'rgb(var(--black-5))',
          4: 'rgb(var(--black-4))',
          3: 'rgb(var(--black-3))',
          2: 'rgb(var(--black-2))',
          1: 'rgb(var(--black-1))',
        },
        white: {
          DEFAULT: 'rgb(var(--white-1))',
          6: 'rgb(var(--white-6))',
          5: 'rgb(var(--white-5))',
          4: 'rgb(var(--white-4))',
          3: 'rgb(var(--white-3))',
          2: 'rgb(var(--white-2))',
          1: 'rgb(var(--white-1))',
        },
        stroke: {
          DEFAULT: 'rgba(var(--coze-stroke-5), var(--coze-stroke-5-alpha))',
          max: 'rgba(var(--coze-stroke-7), var(--coze-stroke-7-alpha))',
          6: 'rgba(var(--coze-stroke-6), var(--coze-stroke-6-alpha))',
          5: 'rgba(var(--coze-stroke-5), var(--coze-stroke-5-alpha))',
          opaque: 'rgb(var(--coze-stroke-opaque))',
        },
        mask: {
          DEFAULT: 'rgba(var(--coze-mask-5), 0.4)',
          5: 'rgba(var(--coze-mask-5), 0.4)',
        },
        icon: {
          DEFAULT: 'hsl(var(--icon))',
          gray: 'hsl(var(--icon-gray))',
          dark: 'hsl(var(--icon-dark))',
        },
        fornax: {
          DEFAULT: 'rgba(var(--coze-fornax-7), 1)',
          7: 'var(--coze-fornax-7)',
        },
      },
      fontSize: {
        mini: 'var(--coze-10)',
        base: 'var(--coze-12)',
        lg: 'var(--coze-14)',
        xl: 'var(--coze-15)',
        xxl: 'var(--coze-16)',
        '18px': 'var(--coze-18)',
        '20px': 'var(--coze-20)',
        '22px': 'var(--coze-22)',
        '24px': 'var(--coze-24)',
        '26px': 'var(--coze-26)',
        '28px': 'var(--coze-28)',
        '30px': 'var(--coze-30)',
        '32px': 'var(--coze-32)',
        '36px': 'var(--coze-36)',
        '48px': 'var(--coze-48)',
        '64px': 'var(--coze-64)',
      },
      spacing: {
        DEFAULT: 'var(--coze-8)',
        xxl: 'var(--coze-96)',
        xl: 'var(--coze-80)',
        md: 'var(--coze-64)',
        mm: 'var(--coze-48)',
        large: 'var(--coze-40)',
        normal: 'var(--coze-32)',
        small: 'var(--coze-20)',
        mini: 'var(--coze-16)',
        '1080px': 'var(--coze-1080)',
        '800px': 'var(--coze-800)',
        '640px': 'var(--coze-640)',
        '480px': 'var(--coze-480)',
        '320px': 'var(--coze-320)',
        '240px': 'var(--coze-240)',
        '200px': 'var(--coze-200)',
        '160px': 'var(--coze-160)',
        '120px': 'var(--coze-120)',
        '96px': 'var(--coze-96)',
        '80px': 'var(--coze-80)',
        '64px': 'var(--coze-64)',
        '48px': 'var(--coze-48)',
        '40px': 'var(--coze-40)',
        '32px': 'var(--coze-32)',
        '30px': 'var(--coze-30)',
        '28px': 'var(--coze-28)',
        '26px': 'var(--coze-26)',
        '24px': 'var(--coze-24)',
        '22px': 'var(--coze-22)',
        '20px': 'var(--coze-20)',
        '18px': 'var(--coze-18)',
        '16px': 'var(--coze-16)',
        '15px': 'var(--coze-15)',
        '14px': 'var(--coze-14)',
        '12px': 'var(--coze-12)',
        '10px': 'var(--coze-10)',
        '9px': 'var(--coze-9)',
        '8px': 'var(--coze-8)',
        '6px': 'var(--coze-6)',
        '5px': 'var(--coze-5)',
        '4px': 'var(--coze-4)',
        '3px': 'var(--coze-3)',
        '2px': 'var(--coze-2)',
        '1px': 'var(--coze-1)',
      },
      borderWidth: {
        DEFAULT: 'var(--coze-1)',
        normal: 'var(--coze-1)',
        half: 'var(--coze-0-5)',
      },
      borderRadius: {
        DEFAULT: 'var(--coze-8)',
        ultra: 'var(--coze-40)',
        xxl: 'var(--coze-24)',
        xl: 'var(--coze-16)',
        md: 'var(--coze-12)',
        m: 'var(--coze-10)',
        normal: 'var(--coze-8)',
        small: 'var(--coze-6)',
        little: 'var(--coze-5)',
        mini: 'var(--coze-4)',
        tiny: 'var(--coze-2)',
      },
      btnBorderRadius: {
        large: 'var(--coze-10)',
        normal: 'var(--coze-8)',
        small: 'var(--coze-5)',
        mini: 'var(--coze-4)',
      },
      inputBorderRadius: {
        large: 'var(--coze-10)',
        normal: 'var(--coze-8)',
        small: 'var(--coze-6)',
      },
      inputHeight: {
        large: 'var(--coze-40)',
        normal: 'var(--coze-32)',
        small: 'var(--coze-24)',
      },
      height: {
        DEFAULT: 'var(--coze-32)',
        '1080px': 'var(--coze-1080)',
        '800px': 'var(--coze-800)',
        '640px': 'var(--coze-640)',
        '480px': 'var(--coze-480)',
        '320px': 'var(--coze-320)',
        '240px': 'var(--coze-240)',
        '200px': 'var(--coze-200)',
        '160px': 'var(--coze-160)',
        '120px': 'var(--coze-120)',
        xxl: 'var(--coze-96)',
        xl: 'var(--coze-80)',
        md: 'var(--coze-64)',
        m: 'var(--coze-48)',
        large: 'var(--coze-40)',
        normal: 'var(--coze-32)',
        small: 'var(--coze-24)',
        petite: 'var(--coze-18)',
        mini: 'var(--coze-16)',
      },
      minHeight: {
        large: 'var(--coze-40)',
        normal: 'var(--coze-32)',
        small: 'var(--coze-24)',
        petite: 'var(--coze-18)',
        mini: 'var(--coze-16)',
      },
      lineHeight: {
        mini: 'var(--coze-16)',
        '12px': 'var(--coze-12)',
        '14px': 'var(--coze-14)',
        '16px': 'var(--coze-16)',
        '20px': 'var(--coze-20)',
        '22px': 'var(--coze-22)',
        '24px': 'var(--coze-24)',
        '28px': 'var(--coze-28)',
        '36px': 'var(--coze-36)',
      },
      width: {
        DEFAULT: 'var(--coze-32)',
        '1080px': 'var(--coze-1080)',
        '800px': 'var(--coze-800)',
        '640px': 'var(--coze-640)',
        '480px': 'var(--coze-480)',
        '320px': 'var(--coze-320)',
        '240px': 'var(--coze-240)',
        '200px': 'var(--coze-200)',
        '160px': 'var(--coze-160)',
        '120px': 'var(--coze-120)',
        xxl: 'var(--coze-96)',
        xl: 'var(--coze-80)',
        md: 'var(--coze-64)',
        m: 'var(--coze-48)',
        large: 'var(--coze-40)',
        normal: 'var(--coze-32)',
        small: 'var(--coze-24)',
        petite: 'var(--coze-18)',
        mini: 'var(--coze-16)',
      },
      minWidth: {
        large: 'var(--coze-40)',
        normal: 'var(--coze-32)',
        small: 'var(--coze-24)',
        petite: 'var(--coze-18)',
        mini: 'var(--coze-16)',
      },
      fill: theme => ({
        DEFAULT: theme('colors.foreground.5'),
        dark: theme('colors.foreground.5'),
        light: theme('colors.foreground.3'),
        black: theme('colors.black'),
        white: theme('colors.white'),
      }),
      boxShadow: {
        DEFAULT:
          '0 4px 12px 0px rgba(var(--coze-shadow-0), 0.08), 0px 8px 24px 0px rgba(var(--coze-shadow-0), 0.04)',
        small:
          '0px 2px 6px 0px rgba(var(--coze-shadow-0), 0.04), 0px 4px 12px 0px  rgba(var(--coze-shadow-0), 0.02)',
        normal:
          '0 4px 12px 0px rgba(var(--coze-shadow-0), 0.08), 0px 8px 24px 0px rgba(var(--coze-shadow-0), 0.04)',
        large:
          '0px 8px 24px 0px rgba(var(--coze-shadow-0), 0.16), 0px 16px 48px 0px rgba(var(--coze-shadow-0), 0.08)',
      },
      keyframes: {
        'icon-down': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(90deg)' },
        },
        'icon-up': {
          '0%': { transform: 'rotate(90deg)' },
          '100%': { transform: 'rotate(0deg)' },
        },
      },
      animation: {
        'icon-down': 'icon-down 0.2s ease-out',
        'icon-up': 'icon-up 0.2s ease-out',
        loading: '',
      },
    },
  },
  plugins: [],
};
