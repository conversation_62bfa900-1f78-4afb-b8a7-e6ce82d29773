{"name": "@coze-arch/tailwind-config", "version": "0.0.1", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.js", "./coze": "./src/coze.js", "./design-token": "./src/design-token.ts"}, "main": "src/index.js", "scripts": {"build": "exit", "dev": "npm run build -- -w", "lint": "eslint ./ --cache --quiet", "test": "exit", "test:cov": "exit"}, "dependencies": {"@coze-arch/monorepo-kits": "workspace:*", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/nesting": "latest", "autoprefixer": "^10.4.16", "fast-glob": "^3.2.11", "postcss": "^8.4.32", "postcss-loader": "^7.3.3", "tailwindcss": "~3.3.3"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@types/node": "^18"}}