{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.node.json", "include": ["src"], "compilerOptions": {"sourceMap": false, "esModuleInterop": true, "target": "ESNext", "strictNullChecks": true, "rootDir": "./src", "outDir": "./output", "tsBuildInfoFile": "output/tsconfig.build.tsbuildinfo"}, "references": [{"path": "../eslint-config/tsconfig.build.json"}, {"path": "../ts-config/tsconfig.build.json"}]}