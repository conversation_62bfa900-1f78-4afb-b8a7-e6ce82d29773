{"name": "@coze-arch/eslint-plugin", "version": "1.0.0", "description": "eslint plugin for flow app", "license": "Apache-2.0", "author": "<EMAIL>", "exports": {".": "./src/index.js", "./zustand": "./src/zustand/index.js"}, "main": "src/index.js", "scripts": {"build": "exit 0", "dev": "npm run build -- -w", "lint": "eslint ./src --cache --quiet", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@typescript-eslint/utils": "~8.10.0", "eslint-module-utils": "~2.8.1", "eslint-rule-composer": "~0.3.0", "eslint-traverse": "^1.0.0", "eslint-utils": "~3.0.0", "semver": "^7.3.7"}, "devDependencies": {"@babel/eslint-parser": "~7.25.8", "@babel/eslint-plugin": "~7.25.7", "@coze-arch/ts-config": "workspace:*", "@eslint/js": "~9.12.0", "@types/eslint": "~9.6.1", "@types/estree": "^1.0.1", "@types/node": "^18", "@types/semver": "^7.3.4", "@typescript-eslint/eslint-plugin": "^8.5.0", "@typescript-eslint/parser": "~8.17.0", "@typescript-eslint/rule-tester": "~8.10.0", "@vitest/coverage-v8": "~3.0.5", "eslint": "~9.12.0", "eslint-config-prettier": "~9.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-prettier": "~5.2.1", "eslint-plugin-react": "~7.37.1", "eslint-plugin-unicorn": "48.0.1", "ts-node": "^10.9.1", "vitest": "~3.0.5"}}