/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { ruleTester } from '../tester';
import { storeNameConvention } from './index';

ruleTester.run('store-name-convention', storeNameConvention, {
  valid: [
    {
      code: "import { create } from 'zustand'; \n const useStore = create()",
    },
    {
      code: "import { create } from 'zustand'; \n const useFooStore = create()",
    },
    {
      code: "import { create } from 'zustand'; \n const createStore = () => { const useFooStore = create() }",
    },
    {
      code: "import { create } from 'foo'; \n const foo = create() ",
    },
  ],
  invalid: [
    {
      code: "import { create } from 'zustand';\n const foo = create()",
      errors: [{ messageId: 'nameConvention' }],
    },
    {
      code: "import { create } from 'zustand';\n createStore = () => {const foo = create()}",
      errors: [{ messageId: 'nameConvention' }],
    },
  ],
});
