{"$schema": "https://developer.microsoft.com/json-schemas/rush/v5/common-versions.schema.json", "ensureConsistentVersions": true, "preferredVersions": {"@coze-arch/coze-design": "0.0.6-alpha.101d0c"}, "allowedAlternativeVersions": {"@coze-arch/coze-design": ["0.0.6-alpha.101d0c"], "@rspack/cli": ["0.6.0"], "@rspack/core": ["0.6.0", ">=0.7"], "@rspack/plugin-react-refresh": ["0.6.0"], "@rsbuild/core": ["0.7.1", "~1.1.0", "1.1.13", "~1.3.1"], "@rsbuild/plugin-svgr": ["0.7.1", "~1.0.5", "~1.0.6", "~1.0.7"], "@rsbuild/plugin-less": ["~1.1.0", "~1.2.1"], "@rsbuild/plugin-react": ["0.7.1", "~1.0.7", "~1.1.0", "~1.1.1"], "@inquirer/prompts": ["^5.3.5"], "react-router": ["^6.22.0", "^4.0.0 || ^5.0.0"], "react-router-dom": ["^6.22.0"], "mobx-react-lite": ["^4.0.7"], "@babel/core": ["^7.26.0"], "@babel/runtime": ["^7.26.0"], "webpack": ["~5.91.0"], "postcss": ["^8.4.49"], "storybook": ["~8.4.2"], "@storybook/addon-essentials": ["~8.4.2"], "@storybook/blocks": ["~8.4.2"], "@storybook/react": ["~8.4.2"], "@swc/helpers": ["0.5.3"], "@rushstack/rush-sdk": ["5.147.1"], "@douyinfe/semi-ui": ["2.49.2", "2.61.0"], "styled-components": [">= 2", ">=4"], "less": ["^3.13.1"], "axios": ["^1.7.1"], "@types/node": ["18.18.9"], "tailwindcss": [">=3.0.0 || insiders"], "less-loader": ["^7.1.0"], "react-is": [">=16.8.0", ">=16.8.0 || >=17.0.0"], "typescript": ["5.7.2"], "@storybook/addon-interactions": ["^7.6.7", "~8.4.2"], "@storybook/addon-links": ["^7.6.7", "~8.4.2"], "@rsbuild/plugin-sass": ["0.7.1", "~1.1.0"], "prop-types": ["^15.5.7", "^15.6.2"], "nanoid": ["3.3.7"], "react-native": [">=0.58"], "ahooks": ["^3.8.0"], "@visactor/vchart": ["1.13.8", "1.11.3"], "@mui/material": ["^5.0.0"]}}