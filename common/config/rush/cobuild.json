/**
 * This configuration file manages <PERSON>'s cobuild feature.
 * More documentation is available on the Rush website: https://rushjs.io
 */
{
  "$schema": "https://developer.microsoft.com/json-schemas/rush/v5/cobuild.schema.json",

  /**
   * (Required) EXPERIMENTAL - Set this to true to enable the cobuild feature.
   * RUSH_COBUILD_CONTEXT_ID should always be specified as an environment variable with an non-empty string,
   * otherwise the cobuild feature will be disabled.
   */
  "cobuildFeatureEnabled": false,

  /**
   * (Required) Choose where cobuild lock will be acquired.
   *
   * The lock provider is registered by the rush plugins.
   * For example, @rushstack/rush-redis-cobuild-plugin registers the "redis" lock provider.
   */
  "cobuildLockProvider": "redis"
}
