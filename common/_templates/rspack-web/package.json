{"name": "{{ packageName }}", "version": "0.0.1", "private": true, "description": "{{ description }}", "keywords": [], "license": "Apache-2.0", "author": "{{ authorName }}", "maintainers": [], "sideEffects": false, "scripts": {"build": "NODE_ENV=production rspack build", "dev": "NODE_ENV=development rspack serve", "lint": "eslint ./ --cache --quiet", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@douyinfe/semi-icons": "^2.36.0", "@douyinfe/semi-ui": "2.61.0", "immer": "^10.0.3", "react": "~18.2.0", "react-dom": "~18.2.0", "react-error-boundary": "^4.0.9", "react-router-dom": "^6.11.1", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@douyinfe/semi-rspack-plugin": "~2.48.0", "@edenx/plugin-tailwind": "1.51.0", "@rspack/cli": "0.4.0", "@rspack/core": "0.4.0", "@rspack/plugin-react-refresh": "0.4.0", "@slardar/web": "~1.12.1", "@svgr/webpack": "^8.1.0", "@types/node": "^18", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "autoprefixer": "^10.4.16", "file-loader": "^6.2.0", "less-loader": "~11.1.3", "postcss": "^8.4.32", "postcss-loader": "^7.3.3", "tailwindcss": "~3.3.3", "ts-morph": "^20.0.0", "ts-node": "^10.9.1", "vitest": "~3.0.5"}}