// Code generated by MockGen. DO NOT EDIT.
// Source: rdb.go
//
// Generated by this command:
//
//	mockgen -destination ../../../internal/mock/infra/contract/rdb/rdb_mock.go --package rdb -source rdb.go
//

// Package rdb is a generated GoMock package.
package rdb

import (
	context "context"
	reflect "reflect"

	rdb "github.com/coze-dev/coze-studio/backend/infra/contract/rdb"
	gomock "go.uber.org/mock/gomock"
)

// MockRDB is a mock of RDB interface.
type MockRDB struct {
	ctrl     *gomock.Controller
	recorder *MockRDBMockRecorder
}

// MockRDBMockRecorder is the mock recorder for MockRDB.
type MockRDBMockRecorder struct {
	mock *MockRDB
}

// NewMockRDB creates a new mock instance.
func NewMockRDB(ctrl *gomock.Controller) *MockRDB {
	mock := &MockRDB{ctrl: ctrl}
	mock.recorder = &MockRDBMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRDB) EXPECT() *MockRDBMockRecorder {
	return m.recorder
}

// AlterTable mocks base method.
func (m *MockRDB) AlterTable(ctx context.Context, req *rdb.AlterTableRequest) (*rdb.AlterTableResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AlterTable", ctx, req)
	ret0, _ := ret[0].(*rdb.AlterTableResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AlterTable indicates an expected call of AlterTable.
func (mr *MockRDBMockRecorder) AlterTable(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AlterTable", reflect.TypeOf((*MockRDB)(nil).AlterTable), ctx, req)
}

// CreateTable mocks base method.
func (m *MockRDB) CreateTable(ctx context.Context, req *rdb.CreateTableRequest) (*rdb.CreateTableResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTable", ctx, req)
	ret0, _ := ret[0].(*rdb.CreateTableResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTable indicates an expected call of CreateTable.
func (mr *MockRDBMockRecorder) CreateTable(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTable", reflect.TypeOf((*MockRDB)(nil).CreateTable), ctx, req)
}

// DeleteData mocks base method.
func (m *MockRDB) DeleteData(ctx context.Context, req *rdb.DeleteDataRequest) (*rdb.DeleteDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteData", ctx, req)
	ret0, _ := ret[0].(*rdb.DeleteDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteData indicates an expected call of DeleteData.
func (mr *MockRDBMockRecorder) DeleteData(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteData", reflect.TypeOf((*MockRDB)(nil).DeleteData), ctx, req)
}

// DropTable mocks base method.
func (m *MockRDB) DropTable(ctx context.Context, req *rdb.DropTableRequest) (*rdb.DropTableResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DropTable", ctx, req)
	ret0, _ := ret[0].(*rdb.DropTableResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DropTable indicates an expected call of DropTable.
func (mr *MockRDBMockRecorder) DropTable(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DropTable", reflect.TypeOf((*MockRDB)(nil).DropTable), ctx, req)
}

// ExecuteSQL mocks base method.
func (m *MockRDB) ExecuteSQL(ctx context.Context, req *rdb.ExecuteSQLRequest) (*rdb.ExecuteSQLResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteSQL", ctx, req)
	ret0, _ := ret[0].(*rdb.ExecuteSQLResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteSQL indicates an expected call of ExecuteSQL.
func (mr *MockRDBMockRecorder) ExecuteSQL(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteSQL", reflect.TypeOf((*MockRDB)(nil).ExecuteSQL), ctx, req)
}

// GetTable mocks base method.
func (m *MockRDB) GetTable(ctx context.Context, req *rdb.GetTableRequest) (*rdb.GetTableResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTable", ctx, req)
	ret0, _ := ret[0].(*rdb.GetTableResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTable indicates an expected call of GetTable.
func (mr *MockRDBMockRecorder) GetTable(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTable", reflect.TypeOf((*MockRDB)(nil).GetTable), ctx, req)
}

// InsertData mocks base method.
func (m *MockRDB) InsertData(ctx context.Context, req *rdb.InsertDataRequest) (*rdb.InsertDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertData", ctx, req)
	ret0, _ := ret[0].(*rdb.InsertDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertData indicates an expected call of InsertData.
func (mr *MockRDBMockRecorder) InsertData(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertData", reflect.TypeOf((*MockRDB)(nil).InsertData), ctx, req)
}

// SelectData mocks base method.
func (m *MockRDB) SelectData(ctx context.Context, req *rdb.SelectDataRequest) (*rdb.SelectDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SelectData", ctx, req)
	ret0, _ := ret[0].(*rdb.SelectDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SelectData indicates an expected call of SelectData.
func (mr *MockRDBMockRecorder) SelectData(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SelectData", reflect.TypeOf((*MockRDB)(nil).SelectData), ctx, req)
}

// UpdateData mocks base method.
func (m *MockRDB) UpdateData(ctx context.Context, req *rdb.UpdateDataRequest) (*rdb.UpdateDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateData", ctx, req)
	ret0, _ := ret[0].(*rdb.UpdateDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateData indicates an expected call of UpdateData.
func (mr *MockRDBMockRecorder) UpdateData(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateData", reflect.TypeOf((*MockRDB)(nil).UpdateData), ctx, req)
}

// UpsertData mocks base method.
func (m *MockRDB) UpsertData(ctx context.Context, req *rdb.UpsertDataRequest) (*rdb.UpsertDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertData", ctx, req)
	ret0, _ := ret[0].(*rdb.UpsertDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertData indicates an expected call of UpsertData.
func (mr *MockRDBMockRecorder) UpsertData(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertData", reflect.TypeOf((*MockRDB)(nil).UpsertData), ctx, req)
}
