// Code generated by MockGen. DO NOT EDIT.
// Source: idgen.go
//
// Generated by this command:
//
//	mockgen -destination ../../../internal/mock/infra/contract/idgen/idgen_mock.go --package mock -source idgen.go
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockIDGenerator is a mock of IDGenerator interface.
type MockIDGenerator struct {
	ctrl     *gomock.Controller
	recorder *MockIDGeneratorMockRecorder
	isgomock struct{}
}

// MockIDGeneratorMockRecorder is the mock recorder for MockIDGenerator.
type MockIDGeneratorMockRecorder struct {
	mock *MockIDGenerator
}

// NewMockIDGenerator creates a new mock instance.
func NewMockIDGenerator(ctrl *gomock.Controller) *MockIDGenerator {
	mock := &MockIDGenerator{ctrl: ctrl}
	mock.recorder = &MockIDGeneratorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDGenerator) EXPECT() *MockIDGeneratorMockRecorder {
	return m.recorder
}

// GenID mocks base method.
func (m *MockIDGenerator) GenID(ctx context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenID", ctx)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenID indicates an expected call of GenID.
func (mr *MockIDGeneratorMockRecorder) GenID(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenID", reflect.TypeOf((*MockIDGenerator)(nil).GenID), ctx)
}

// GenMultiIDs mocks base method.
func (m *MockIDGenerator) GenMultiIDs(ctx context.Context, counts int) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenMultiIDs", ctx, counts)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenMultiIDs indicates an expected call of GenMultiIDs.
func (mr *MockIDGeneratorMockRecorder) GenMultiIDs(ctx, counts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenMultiIDs", reflect.TypeOf((*MockIDGenerator)(nil).GenMultiIDs), ctx, counts)
}
