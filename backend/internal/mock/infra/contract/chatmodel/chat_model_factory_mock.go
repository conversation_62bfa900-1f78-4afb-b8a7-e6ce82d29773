// Code generated by MockGen. DO NOT EDIT.
// Source: chat_model.go
//
// Generated by this command:
//
//	mockgen -destination ../../../internal/mock/infra/contract/chatmodel/chat_model_factory_mock.go -package mock -source chat_model.go Factory
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	chatmodel "github.com/coze-dev/coze-studio/backend/infra/contract/chatmodel"
	gomock "go.uber.org/mock/gomock"
)

// MockFactory is a mock of Factory interface.
type MockFactory struct {
	ctrl     *gomock.Controller
	recorder *MockFactoryMockRecorder
	isgomock struct{}
}

// MockFactoryMockRecorder is the mock recorder for MockFactory.
type MockFactoryMockRecorder struct {
	mock *MockFactory
}

// NewMockFactory creates a new mock instance.
func NewMockFactory(ctrl *gomock.Controller) *MockFactory {
	mock := &MockFactory{ctrl: ctrl}
	mock.recorder = &MockFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFactory) EXPECT() *MockFactoryMockRecorder {
	return m.recorder
}

// CreateChatModel mocks base method.
func (m *MockFactory) CreateChatModel(ctx context.Context, protocol chatmodel.Protocol, config *chatmodel.Config) (chatmodel.ToolCallingChatModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChatModel", ctx, protocol, config)
	ret0, _ := ret[0].(chatmodel.ToolCallingChatModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChatModel indicates an expected call of CreateChatModel.
func (mr *MockFactoryMockRecorder) CreateChatModel(ctx, protocol, config any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChatModel", reflect.TypeOf((*MockFactory)(nil).CreateChatModel), ctx, protocol, config)
}

// SupportProtocol mocks base method.
func (m *MockFactory) SupportProtocol(protocol chatmodel.Protocol) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportProtocol", protocol)
	ret0, _ := ret[0].(bool)
	return ret0
}

// SupportProtocol indicates an expected call of SupportProtocol.
func (mr *MockFactoryMockRecorder) SupportProtocol(protocol any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportProtocol", reflect.TypeOf((*MockFactory)(nil).SupportProtocol), protocol)
}
