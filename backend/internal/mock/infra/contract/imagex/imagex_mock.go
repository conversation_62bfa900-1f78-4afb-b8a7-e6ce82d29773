// Code generated by MockGen. DO NOT EDIT.
// Source: imagex.go
//
// Generated by this command:
//
//	mockgen -destination ../../../internal/mock/infra/contract/imagex/imagex_mock.go --package imagex -source imagex.go
//

// Package imagex is a generated GoMock package.
package imagex

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"

	imagex "github.com/coze-dev/coze-studio/backend/infra/contract/imagex"
)

// MockImageX is a mock of ImageX interface.
type MockImageX struct {
	ctrl     *gomock.Controller
	recorder *MockImageXMockRecorder
	isgomock struct{}
}

// MockImageXMockRecorder is the mock recorder for MockImageX.
type MockImageXMockRecorder struct {
	mock *MockImageX
}

// NewMockImageX creates a new mock instance.
func NewMockImageX(ctrl *gomock.Controller) *MockImageX {
	mock := &MockImageX{ctrl: ctrl}
	mock.recorder = &MockImageXMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockImageX) EXPECT() *MockImageXMockRecorder {
	return m.recorder
}

// GetResourceURL mocks base method.
func (m *MockImageX) GetResourceURL(ctx context.Context, uri string, opts ...imagex.GetResourceOpt) (*imagex.ResourceURL, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, uri}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetResourceURL", varargs...)
	ret0, _ := ret[0].(*imagex.ResourceURL)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResourceURL indicates an expected call of GetResourceURL.
func (mr *MockImageXMockRecorder) GetResourceURL(ctx, uri any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, uri}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResourceURL", reflect.TypeOf((*MockImageX)(nil).GetResourceURL), varargs...)
}

// GetServerID mocks base method.
func (m *MockImageX) GetServerID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServerID")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetServerID indicates an expected call of GetServerID.
func (mr *MockImageXMockRecorder) GetServerID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServerID", reflect.TypeOf((*MockImageX)(nil).GetServerID))
}

// GetUploadAuth mocks base method.
func (m *MockImageX) GetUploadAuth(ctx context.Context, opt ...imagex.UploadAuthOpt) (*imagex.SecurityToken, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx}
	for _, a := range opt {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUploadAuth", varargs...)
	ret0, _ := ret[0].(*imagex.SecurityToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUploadAuth indicates an expected call of GetUploadAuth.
func (mr *MockImageXMockRecorder) GetUploadAuth(ctx any, opt ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx}, opt...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUploadAuth", reflect.TypeOf((*MockImageX)(nil).GetUploadAuth), varargs...)
}

// GetUploadAuthWithExpire mocks base method.
func (m *MockImageX) GetUploadAuthWithExpire(ctx context.Context, expire time.Duration, opt ...imagex.UploadAuthOpt) (*imagex.SecurityToken, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, expire}
	for _, a := range opt {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUploadAuthWithExpire", varargs...)
	ret0, _ := ret[0].(*imagex.SecurityToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUploadAuthWithExpire indicates an expected call of GetUploadAuthWithExpire.
func (mr *MockImageXMockRecorder) GetUploadAuthWithExpire(ctx, expire any, opt ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, expire}, opt...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUploadAuthWithExpire", reflect.TypeOf((*MockImageX)(nil).GetUploadAuthWithExpire), varargs...)
}

// GetUploadHost mocks base method.
func (m *MockImageX) GetUploadHost() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUploadHost")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetUploadHost indicates an expected call of GetUploadHost.
func (mr *MockImageXMockRecorder) GetUploadHost() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUploadHost", reflect.TypeOf((*MockImageX)(nil).GetUploadHost))
}

// Upload mocks base method.
func (m *MockImageX) Upload(ctx context.Context, data []byte, opts ...imagex.UploadAuthOpt) (*imagex.UploadResult, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, data}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Upload", varargs...)
	ret0, _ := ret[0].(*imagex.UploadResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Upload indicates an expected call of Upload.
func (mr *MockImageXMockRecorder) Upload(ctx, data any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, data}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upload", reflect.TypeOf((*MockImageX)(nil).Upload), varargs...)
}
