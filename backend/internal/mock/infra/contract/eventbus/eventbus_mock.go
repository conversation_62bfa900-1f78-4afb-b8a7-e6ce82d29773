// Code generated by MockGen. DO NOT EDIT.
// Source: eventbus.go
//
// Generated by this command:
//
//	mockgen -destination ../../../internal/mock/infra/contract/eventbus/eventbus_mock.go -package mock -source eventbus.go Factory
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	eventbus "github.com/coze-dev/coze-studio/backend/infra/contract/eventbus"
	gomock "go.uber.org/mock/gomock"
)

// MockProducer is a mock of Producer interface.
type MockProducer struct {
	ctrl     *gomock.Controller
	recorder *MockProducerMockRecorder
	isgomock struct{}
}

// MockProducerMockRecorder is the mock recorder for MockProducer.
type MockProducerMockRecorder struct {
	mock *MockProducer
}

// NewMockProducer creates a new mock instance.
func NewMockProducer(ctrl *gomock.Controller) *MockProducer {
	mock := &MockProducer{ctrl: ctrl}
	mock.recorder = &MockProducerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProducer) EXPECT() *MockProducerMockRecorder {
	return m.recorder
}

// BatchSend mocks base method.
func (m *MockProducer) BatchSend(ctx context.Context, bodyArr [][]byte, opts ...eventbus.SendOpt) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, bodyArr}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchSend", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSend indicates an expected call of BatchSend.
func (mr *MockProducerMockRecorder) BatchSend(ctx, bodyArr any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, bodyArr}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSend", reflect.TypeOf((*MockProducer)(nil).BatchSend), varargs...)
}

// Send mocks base method.
func (m *MockProducer) Send(ctx context.Context, body []byte, opts ...eventbus.SendOpt) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, body}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Send", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Send indicates an expected call of Send.
func (mr *MockProducerMockRecorder) Send(ctx, body any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, body}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockProducer)(nil).Send), varargs...)
}

// MockConsumer is a mock of Consumer interface.
type MockConsumer struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerMockRecorder
	isgomock struct{}
}

// MockConsumerMockRecorder is the mock recorder for MockConsumer.
type MockConsumerMockRecorder struct {
	mock *MockConsumer
}

// NewMockConsumer creates a new mock instance.
func NewMockConsumer(ctrl *gomock.Controller) *MockConsumer {
	mock := &MockConsumer{ctrl: ctrl}
	mock.recorder = &MockConsumerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumer) EXPECT() *MockConsumerMockRecorder {
	return m.recorder
}

// MockConsumerHandler is a mock of ConsumerHandler interface.
type MockConsumerHandler struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerHandlerMockRecorder
	isgomock struct{}
}

// MockConsumerHandlerMockRecorder is the mock recorder for MockConsumerHandler.
type MockConsumerHandlerMockRecorder struct {
	mock *MockConsumerHandler
}

// NewMockConsumerHandler creates a new mock instance.
func NewMockConsumerHandler(ctrl *gomock.Controller) *MockConsumerHandler {
	mock := &MockConsumerHandler{ctrl: ctrl}
	mock.recorder = &MockConsumerHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerHandler) EXPECT() *MockConsumerHandlerMockRecorder {
	return m.recorder
}

// HandleMessage mocks base method.
func (m *MockConsumerHandler) HandleMessage(ctx context.Context, msg *eventbus.Message) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleMessage", ctx, msg)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleMessage indicates an expected call of HandleMessage.
func (mr *MockConsumerHandlerMockRecorder) HandleMessage(ctx, msg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleMessage", reflect.TypeOf((*MockConsumerHandler)(nil).HandleMessage), ctx, msg)
}
