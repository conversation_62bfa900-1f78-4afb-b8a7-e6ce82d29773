// Code generated by MockGen. DO NOT EDIT.
// Source: code.go
//
// Generated by this command:
//
//	mockgen -destination ../../../internal/mock/domain/workflow/crossdomain/code/code_mock.go --package code -source code.go
//

// Package code is a generated GoMock package.
package code

import (
	context "context"
	reflect "reflect"

	coderunner "github.com/coze-dev/coze-studio/backend/infra/contract/coderunner"
	gomock "go.uber.org/mock/gomock"
)

// MockRunner is a mock of Runner interface.
type MockRunner struct {
	ctrl     *gomock.Controller
	recorder *MockRunnerMockRecorder
}

// MockRunnerMockRecorder is the mock recorder for MockRunner.
type MockRunnerMockRecorder struct {
	mock *MockRunner
}

// NewMockRunner creates a new mock instance.
func NewMockRunner(ctrl *gomock.Controller) *MockRunner {
	mock := &MockRunner{ctrl: ctrl}
	mock.recorder = &MockRunnerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRunner) EXPECT() *MockRunnerMockRecorder {
	return m.recorder
}

// Run mocks base method.
func (m *MockRunner) Run(ctx context.Context, request *coderunner.RunRequest) (*coderunner.RunResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Run", ctx, request)
	ret0, _ := ret[0].(*coderunner.RunResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Run indicates an expected call of Run.
func (mr *MockRunnerMockRecorder) Run(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Run", reflect.TypeOf((*MockRunner)(nil).Run), ctx, request)
}
