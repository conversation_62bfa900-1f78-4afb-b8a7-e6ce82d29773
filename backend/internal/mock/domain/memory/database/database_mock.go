// Code generated by MockGen. DO NOT EDIT.
// Source: database.go
//
// Generated by this command:
//
//	mockgen -destination ../../../../internal/mock/domain/memory/database/database_mock.go --package database -source database.go
//

// Package database is a generated GoMock package.
package database

import (
	context "context"
	reflect "reflect"

	service "github.com/coze-dev/coze-studio/backend/domain/memory/database/service"
	gomock "go.uber.org/mock/gomock"
)

// MockDatabase is a mock of Database interface.
type MockDatabase struct {
	ctrl     *gomock.Controller
	recorder *MockDatabaseMockRecorder
}

// MockDatabaseMockRecorder is the mock recorder for MockDatabase.
type MockDatabaseMockRecorder struct {
	mock *MockDatabase
}

// NewMockDatabase creates a new mock instance.
func NewMockDatabase(ctrl *gomock.Controller) *MockDatabase {
	mock := &MockDatabase{ctrl: ctrl}
	mock.recorder = &MockDatabaseMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDatabase) EXPECT() *MockDatabaseMockRecorder {
	return m.recorder
}

// AddDatabaseRecord mocks base method.
func (m *MockDatabase) AddDatabaseRecord(ctx context.Context, req *service.AddDatabaseRecordRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddDatabaseRecord", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddDatabaseRecord indicates an expected call of AddDatabaseRecord.
func (mr *MockDatabaseMockRecorder) AddDatabaseRecord(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddDatabaseRecord", reflect.TypeOf((*MockDatabase)(nil).AddDatabaseRecord), ctx, req)
}

// BindDatabase mocks base method.
func (m *MockDatabase) BindDatabase(ctx context.Context, req *service.BindDatabaseToAgentRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindDatabase", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindDatabase indicates an expected call of BindDatabase.
func (mr *MockDatabaseMockRecorder) BindDatabase(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindDatabase", reflect.TypeOf((*MockDatabase)(nil).BindDatabase), ctx, req)
}

// CreateDatabase mocks base method.
func (m *MockDatabase) CreateDatabase(ctx context.Context, req *service.CreateDatabaseRequest) (*service.CreateDatabaseResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDatabase", ctx, req)
	ret0, _ := ret[0].(*service.CreateDatabaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDatabase indicates an expected call of CreateDatabase.
func (mr *MockDatabaseMockRecorder) CreateDatabase(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDatabase", reflect.TypeOf((*MockDatabase)(nil).CreateDatabase), ctx, req)
}

// DeleteDatabase mocks base method.
func (m *MockDatabase) DeleteDatabase(ctx context.Context, req *service.DeleteDatabaseRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteDatabase", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteDatabase indicates an expected call of DeleteDatabase.
func (mr *MockDatabaseMockRecorder) DeleteDatabase(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteDatabase", reflect.TypeOf((*MockDatabase)(nil).DeleteDatabase), ctx, req)
}

// DeleteDatabaseByAppID mocks base method.
func (m *MockDatabase) DeleteDatabaseByAppID(ctx context.Context, req *service.DeleteDatabaseByAppIDRequest) (*service.DeleteDatabaseByAppIDResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteDatabaseByAppID", ctx, req)
	ret0, _ := ret[0].(*service.DeleteDatabaseByAppIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteDatabaseByAppID indicates an expected call of DeleteDatabaseByAppID.
func (mr *MockDatabaseMockRecorder) DeleteDatabaseByAppID(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteDatabaseByAppID", reflect.TypeOf((*MockDatabase)(nil).DeleteDatabaseByAppID), ctx, req)
}

// DeleteDatabaseRecord mocks base method.
func (m *MockDatabase) DeleteDatabaseRecord(ctx context.Context, req *service.DeleteDatabaseRecordRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteDatabaseRecord", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteDatabaseRecord indicates an expected call of DeleteDatabaseRecord.
func (mr *MockDatabaseMockRecorder) DeleteDatabaseRecord(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteDatabaseRecord", reflect.TypeOf((*MockDatabase)(nil).DeleteDatabaseRecord), ctx, req)
}

// ExecuteSQL mocks base method.
func (m *MockDatabase) ExecuteSQL(ctx context.Context, req *service.ExecuteSQLRequest) (*service.ExecuteSQLResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteSQL", ctx, req)
	ret0, _ := ret[0].(*service.ExecuteSQLResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteSQL indicates an expected call of ExecuteSQL.
func (mr *MockDatabaseMockRecorder) ExecuteSQL(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteSQL", reflect.TypeOf((*MockDatabase)(nil).ExecuteSQL), ctx, req)
}

// GetAllDatabaseByAppID mocks base method.
func (m *MockDatabase) GetAllDatabaseByAppID(ctx context.Context, req *service.GetAllDatabaseByAppIDRequest) (*service.GetAllDatabaseByAppIDResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllDatabaseByAppID", ctx, req)
	ret0, _ := ret[0].(*service.GetAllDatabaseByAppIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllDatabaseByAppID indicates an expected call of GetAllDatabaseByAppID.
func (mr *MockDatabaseMockRecorder) GetAllDatabaseByAppID(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllDatabaseByAppID", reflect.TypeOf((*MockDatabase)(nil).GetAllDatabaseByAppID), ctx, req)
}

// GetDatabaseFileProgressData mocks base method.
func (m *MockDatabase) GetDatabaseFileProgressData(ctx context.Context, req *service.GetDatabaseFileProgressDataRequest) (*service.GetDatabaseFileProgressDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDatabaseFileProgressData", ctx, req)
	ret0, _ := ret[0].(*service.GetDatabaseFileProgressDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDatabaseFileProgressData indicates an expected call of GetDatabaseFileProgressData.
func (mr *MockDatabaseMockRecorder) GetDatabaseFileProgressData(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDatabaseFileProgressData", reflect.TypeOf((*MockDatabase)(nil).GetDatabaseFileProgressData), ctx, req)
}

// GetDatabaseTableSchema mocks base method.
func (m *MockDatabase) GetDatabaseTableSchema(ctx context.Context, req *service.GetDatabaseTableSchemaRequest) (*service.GetDatabaseTableSchemaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDatabaseTableSchema", ctx, req)
	ret0, _ := ret[0].(*service.GetDatabaseTableSchemaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDatabaseTableSchema indicates an expected call of GetDatabaseTableSchema.
func (mr *MockDatabaseMockRecorder) GetDatabaseTableSchema(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDatabaseTableSchema", reflect.TypeOf((*MockDatabase)(nil).GetDatabaseTableSchema), ctx, req)
}

// GetDatabaseTemplate mocks base method.
func (m *MockDatabase) GetDatabaseTemplate(ctx context.Context, req *service.GetDatabaseTemplateRequest) (*service.GetDatabaseTemplateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDatabaseTemplate", ctx, req)
	ret0, _ := ret[0].(*service.GetDatabaseTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDatabaseTemplate indicates an expected call of GetDatabaseTemplate.
func (mr *MockDatabaseMockRecorder) GetDatabaseTemplate(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDatabaseTemplate", reflect.TypeOf((*MockDatabase)(nil).GetDatabaseTemplate), ctx, req)
}

// GetDraftDatabaseByOnlineID mocks base method.
func (m *MockDatabase) GetDraftDatabaseByOnlineID(ctx context.Context, req *service.GetDraftDatabaseByOnlineIDRequest) (*service.GetDraftDatabaseByOnlineIDResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDraftDatabaseByOnlineID", ctx, req)
	ret0, _ := ret[0].(*service.GetDraftDatabaseByOnlineIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDraftDatabaseByOnlineID indicates an expected call of GetDraftDatabaseByOnlineID.
func (mr *MockDatabaseMockRecorder) GetDraftDatabaseByOnlineID(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDraftDatabaseByOnlineID", reflect.TypeOf((*MockDatabase)(nil).GetDraftDatabaseByOnlineID), ctx, req)
}

// ListDatabase mocks base method.
func (m *MockDatabase) ListDatabase(ctx context.Context, req *service.ListDatabaseRequest) (*service.ListDatabaseResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListDatabase", ctx, req)
	ret0, _ := ret[0].(*service.ListDatabaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListDatabase indicates an expected call of ListDatabase.
func (mr *MockDatabaseMockRecorder) ListDatabase(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListDatabase", reflect.TypeOf((*MockDatabase)(nil).ListDatabase), ctx, req)
}

// ListDatabaseRecord mocks base method.
func (m *MockDatabase) ListDatabaseRecord(ctx context.Context, req *service.ListDatabaseRecordRequest) (*service.ListDatabaseRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListDatabaseRecord", ctx, req)
	ret0, _ := ret[0].(*service.ListDatabaseRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListDatabaseRecord indicates an expected call of ListDatabaseRecord.
func (mr *MockDatabaseMockRecorder) ListDatabaseRecord(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListDatabaseRecord", reflect.TypeOf((*MockDatabase)(nil).ListDatabaseRecord), ctx, req)
}

// MGetDatabase mocks base method.
func (m *MockDatabase) MGetDatabase(ctx context.Context, req *service.MGetDatabaseRequest) (*service.MGetDatabaseResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetDatabase", ctx, req)
	ret0, _ := ret[0].(*service.MGetDatabaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetDatabase indicates an expected call of MGetDatabase.
func (mr *MockDatabaseMockRecorder) MGetDatabase(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetDatabase", reflect.TypeOf((*MockDatabase)(nil).MGetDatabase), ctx, req)
}

// MGetDatabaseByAgentID mocks base method.
func (m *MockDatabase) MGetDatabaseByAgentID(ctx context.Context, req *service.MGetDatabaseByAgentIDRequest) (*service.MGetDatabaseByAgentIDResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetDatabaseByAgentID", ctx, req)
	ret0, _ := ret[0].(*service.MGetDatabaseByAgentIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetDatabaseByAgentID indicates an expected call of MGetDatabaseByAgentID.
func (mr *MockDatabaseMockRecorder) MGetDatabaseByAgentID(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetDatabaseByAgentID", reflect.TypeOf((*MockDatabase)(nil).MGetDatabaseByAgentID), ctx, req)
}

// MGetRelationsByAgentID mocks base method.
func (m *MockDatabase) MGetRelationsByAgentID(ctx context.Context, req *service.MGetRelationsByAgentIDRequest) (*service.MGetRelationsByAgentIDResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetRelationsByAgentID", ctx, req)
	ret0, _ := ret[0].(*service.MGetRelationsByAgentIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetRelationsByAgentID indicates an expected call of MGetRelationsByAgentID.
func (mr *MockDatabaseMockRecorder) MGetRelationsByAgentID(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetRelationsByAgentID", reflect.TypeOf((*MockDatabase)(nil).MGetRelationsByAgentID), ctx, req)
}

// PublishDatabase mocks base method.
func (m *MockDatabase) PublishDatabase(ctx context.Context, req *service.PublishDatabaseRequest) (*service.PublishDatabaseResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishDatabase", ctx, req)
	ret0, _ := ret[0].(*service.PublishDatabaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PublishDatabase indicates an expected call of PublishDatabase.
func (mr *MockDatabaseMockRecorder) PublishDatabase(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishDatabase", reflect.TypeOf((*MockDatabase)(nil).PublishDatabase), ctx, req)
}

// SubmitDatabaseInsertTask mocks base method.
func (m *MockDatabase) SubmitDatabaseInsertTask(ctx context.Context, req *service.SubmitDatabaseInsertTaskRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitDatabaseInsertTask", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// SubmitDatabaseInsertTask indicates an expected call of SubmitDatabaseInsertTask.
func (mr *MockDatabaseMockRecorder) SubmitDatabaseInsertTask(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitDatabaseInsertTask", reflect.TypeOf((*MockDatabase)(nil).SubmitDatabaseInsertTask), ctx, req)
}

// UnBindDatabase mocks base method.
func (m *MockDatabase) UnBindDatabase(ctx context.Context, req *service.UnBindDatabaseToAgentRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnBindDatabase", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnBindDatabase indicates an expected call of UnBindDatabase.
func (mr *MockDatabaseMockRecorder) UnBindDatabase(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnBindDatabase", reflect.TypeOf((*MockDatabase)(nil).UnBindDatabase), ctx, req)
}

// UpdateDatabase mocks base method.
func (m *MockDatabase) UpdateDatabase(ctx context.Context, req *service.UpdateDatabaseRequest) (*service.UpdateDatabaseResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDatabase", ctx, req)
	ret0, _ := ret[0].(*service.UpdateDatabaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateDatabase indicates an expected call of UpdateDatabase.
func (mr *MockDatabaseMockRecorder) UpdateDatabase(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDatabase", reflect.TypeOf((*MockDatabase)(nil).UpdateDatabase), ctx, req)
}

// UpdateDatabaseRecord mocks base method.
func (m *MockDatabase) UpdateDatabaseRecord(ctx context.Context, req *service.UpdateDatabaseRecordRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDatabaseRecord", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDatabaseRecord indicates an expected call of UpdateDatabaseRecord.
func (mr *MockDatabaseMockRecorder) UpdateDatabaseRecord(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDatabaseRecord", reflect.TypeOf((*MockDatabase)(nil).UpdateDatabaseRecord), ctx, req)
}

// ValidateDatabaseTableSchema mocks base method.
func (m *MockDatabase) ValidateDatabaseTableSchema(ctx context.Context, req *service.ValidateDatabaseTableSchemaRequest) (*service.ValidateDatabaseTableSchemaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateDatabaseTableSchema", ctx, req)
	ret0, _ := ret[0].(*service.ValidateDatabaseTableSchemaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateDatabaseTableSchema indicates an expected call of ValidateDatabaseTableSchema.
func (mr *MockDatabaseMockRecorder) ValidateDatabaseTableSchema(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateDatabaseTableSchema", reflect.TypeOf((*MockDatabase)(nil).ValidateDatabaseTableSchema), ctx, req)
}
