// Code generated by MockGen. DO NOT EDIT.
// Source: search.go
//
// Generated by this command:
//
//	mockgen -destination ../../../../internal/mock/domain/memory/database/crossdomain/search.go --package database -source search.go
//

// Package database is a generated GoMock package.
package database

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"

	entity "github.com/coze-dev/coze-studio/backend/domain/search/entity"
)

// MockResourceDomainNotifier is a mock of ResourceDomainNotifier interface.
type MockResourceDomainNotifier struct {
	ctrl     *gomock.Controller
	recorder *MockResourceDomainNotifierMockRecorder
}

// MockResourceDomainNotifierMockRecorder is the mock recorder for MockResourceDomainNotifier.
type MockResourceDomainNotifierMockRecorder struct {
	mock *MockResourceDomainNotifier
}

// NewMockResourceDomainNotifier creates a new mock instance.
func NewMockResourceDomainNotifier(ctrl *gomock.Controller) *MockResourceDomainNotifier {
	mock := &MockResourceDomainNotifier{ctrl: ctrl}
	mock.recorder = &MockResourceDomainNotifierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockResourceDomainNotifier) EXPECT() *MockResourceDomainNotifierMockRecorder {
	return m.recorder
}

// PublishResources mocks base method.
func (m *MockResourceDomainNotifier) PublishResources(ctx context.Context, event *entity.ResourceDomainEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishResources", ctx, event)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishResources indicates an expected call of PublishResources.
func (mr *MockResourceDomainNotifierMockRecorder) PublishResources(ctx, event any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishResources", reflect.TypeOf((*MockResourceDomainNotifier)(nil).PublishResources), ctx, event)
}
