// Code generated by MockGen. DO NOT EDIT.
// Source: knowledge.go
//
// Generated by this command:
//
//	mockgen -destination ../../../../internal/mock/domain/agent/singleagent/knowledge_mock.go --package mock -source knowledge.go
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	knowledge "github.com/coze-dev/coze-studio/backend/domain/knowledge/service"

	gomock "go.uber.org/mock/gomock"
)

// MockKnowledge is a mock of Knowledge interface.
type MockKnowledge struct {
	ctrl     *gomock.Controller
	recorder *MockKnowledgeMockRecorder
	isgomock struct{}
}

// MockKnowledgeMockRecorder is the mock recorder for MockKnowledge.
type MockKnowledgeMockRecorder struct {
	mock *MockKnowledge
}

// NewMockKnowledge creates a new mock instance.
func NewMockKnowledge(ctrl *gomock.Controller) *MockKnowledge {
	mock := &MockKnowledge{ctrl: ctrl}
	mock.recorder = &MockKnowledgeMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKnowledge) EXPECT() *MockKnowledgeMockRecorder {
	return m.recorder
}

// ListKnowledge mocks base method.
func (m *MockKnowledge) ListKnowledge(ctx context.Context, request *knowledge.ListKnowledgeRequest) (*knowledge.ListKnowledgeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListKnowledge", ctx, request)
	ret0, _ := ret[0].(*knowledge.ListKnowledgeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListKnowledge indicates an expected call of ListKnowledge.
func (mr *MockKnowledgeMockRecorder) ListKnowledge(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListKnowledge", reflect.TypeOf((*MockKnowledge)(nil).ListKnowledge), ctx, request)
}

// Retrieve mocks base method.
func (m *MockKnowledge) Retrieve(ctx context.Context, req *knowledge.RetrieveRequest) (*knowledge.RetrieveResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Retrieve", ctx, req)
	ret0, _ := ret[0].(*knowledge.RetrieveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Retrieve indicates an expected call of Retrieve.
func (mr *MockKnowledgeMockRecorder) Retrieve(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Retrieve", reflect.TypeOf((*MockKnowledge)(nil).Retrieve), ctx, req)
}
