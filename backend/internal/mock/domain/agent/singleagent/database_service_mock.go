// Code generated by MockGen. DO NOT EDIT.
// Source: database.go
//
// Generated by this command:
//
//	mockgen -destination ../../../../internal/mock/domain/agent/singleagent/database_service_mock.go --package mock -source database.go
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	service "github.com/coze-dev/coze-studio/backend/domain/memory/database/service"
	gomock "go.uber.org/mock/gomock"
)

// MockDatabase is a mock of Database interface.
type MockDatabase struct {
	ctrl     *gomock.Controller
	recorder *MockDatabaseMockRecorder
}

// MockDatabaseMockRecorder is the mock recorder for MockDatabase.
type MockDatabaseMockRecorder struct {
	mock *MockDatabase
}

// NewMockDatabase creates a new mock instance.
func NewMockDatabase(ctrl *gomock.Controller) *MockDatabase {
	mock := &MockDatabase{ctrl: ctrl}
	mock.recorder = &MockDatabaseMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDatabase) EXPECT() *MockDatabaseMockRecorder {
	return m.recorder
}

// ExecuteSQL mocks base method.
func (m *MockDatabase) ExecuteSQL(ctx context.Context, req *service.ExecuteSQLRequest) (*service.ExecuteSQLResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteSQL", ctx, req)
	ret0, _ := ret[0].(*service.ExecuteSQLResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteSQL indicates an expected call of ExecuteSQL.
func (mr *MockDatabaseMockRecorder) ExecuteSQL(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteSQL", reflect.TypeOf((*MockDatabase)(nil).ExecuteSQL), ctx, req)
}

// PublishDatabase mocks base method.
func (m *MockDatabase) PublishDatabase(ctx context.Context, req *service.PublishDatabaseRequest) (*service.PublishDatabaseResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishDatabase", ctx, req)
	ret0, _ := ret[0].(*service.PublishDatabaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PublishDatabase indicates an expected call of PublishDatabase.
func (mr *MockDatabaseMockRecorder) PublishDatabase(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishDatabase", reflect.TypeOf((*MockDatabase)(nil).PublishDatabase), ctx, req)
}
