// Code generated by MockGen. DO NOT EDIT.
// Source: backend/infra/contract/modelmgr/modelmgr.go
//
// Generated by this command:
//
//	mockgen --destination backend/internal/mock/domain/agent/singleagent/model_mgr_mock.go --package mock -source backend/infra/contract/modelmgr/modelmgr.go
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	modelmgr "github.com/coze-dev/coze-studio/backend/infra/contract/modelmgr"
	gomock "go.uber.org/mock/gomock"
)

// MockManager is a mock of Manager interface.
type MockManager struct {
	ctrl     *gomock.Controller
	recorder *MockManagerMockRecorder
}

// MockManagerMockRecorder is the mock recorder for MockManager.
type MockManagerMockRecorder struct {
	mock *MockManager
}

// NewMockManager creates a new mock instance.
func NewMockManager(ctrl *gomock.Controller) *MockManager {
	mock := &MockManager{ctrl: ctrl}
	mock.recorder = &MockManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockManager) EXPECT() *MockManagerMockRecorder {
	return m.recorder
}

// ListModel mocks base method.
func (m *MockManager) ListModel(ctx context.Context, req *modelmgr.ListModelRequest) (*modelmgr.ListModelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListModel", ctx, req)
	ret0, _ := ret[0].(*modelmgr.ListModelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListModel indicates an expected call of ListModel.
func (mr *MockManagerMockRecorder) ListModel(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListModel", reflect.TypeOf((*MockManager)(nil).ListModel), ctx, req)
}

// MGetModelByID mocks base method.
func (m *MockManager) MGetModelByID(ctx context.Context, req *modelmgr.MGetModelRequest) ([]*modelmgr.Model, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetModelByID", ctx, req)
	ret0, _ := ret[0].([]*modelmgr.Model)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetModelByID indicates an expected call of MGetModelByID.
func (mr *MockManagerMockRecorder) MGetModelByID(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetModelByID", reflect.TypeOf((*MockManager)(nil).MGetModelByID), ctx, req)
}
