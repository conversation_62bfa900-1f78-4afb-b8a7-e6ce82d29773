// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package workflow

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
	"github.com/coze-dev/coze-studio/backend/api/model/base"
)

type FrontedTagType int64

const (
	// Text
	FrontedTagType_TEXT FrontedTagType = 0
	// Time, with timestamp, in milliseconds
	FrontedTagType_TIME FrontedTagType = 1
	// Time interval, in milliseconds
	FrontedTagType_TIME_DURATION FrontedTagType = 2
)

func (p FrontedTagType) String() string {
	switch p {
	case FrontedTagType_TEXT:
		return "TEXT"
	case FrontedTagType_TIME:
		return "TIME"
	case FrontedTagType_TIME_DURATION:
		return "TIME_DURATION"
	}
	return "<UNSET>"
}

func FrontedTagTypeFromString(s string) (FrontedTagType, error) {
	switch s {
	case "TEXT":
		return FrontedTagType_TEXT, nil
	case "TIME":
		return FrontedTagType_TIME, nil
	case "TIME_DURATION":
		return FrontedTagType_TIME_DURATION, nil
	}
	return FrontedTagType(0), fmt.Errorf("not a valid FrontedTagType string")
}

func FrontedTagTypePtr(v FrontedTagType) *FrontedTagType { return &v }
func (p *FrontedTagType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = FrontedTagType(result.Int64)
	return
}

func (p *FrontedTagType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type QueryTypeEnum int64

const (
	QueryTypeEnum_Undefined QueryTypeEnum = 0
	QueryTypeEnum_Match     QueryTypeEnum = 1
	QueryTypeEnum_Term      QueryTypeEnum = 2
	QueryTypeEnum_Range     QueryTypeEnum = 3
	QueryTypeEnum_Exist     QueryTypeEnum = 4
	QueryTypeEnum_NotExist  QueryTypeEnum = 5
)

func (p QueryTypeEnum) String() string {
	switch p {
	case QueryTypeEnum_Undefined:
		return "Undefined"
	case QueryTypeEnum_Match:
		return "Match"
	case QueryTypeEnum_Term:
		return "Term"
	case QueryTypeEnum_Range:
		return "Range"
	case QueryTypeEnum_Exist:
		return "Exist"
	case QueryTypeEnum_NotExist:
		return "NotExist"
	}
	return "<UNSET>"
}

func QueryTypeEnumFromString(s string) (QueryTypeEnum, error) {
	switch s {
	case "Undefined":
		return QueryTypeEnum_Undefined, nil
	case "Match":
		return QueryTypeEnum_Match, nil
	case "Term":
		return QueryTypeEnum_Term, nil
	case "Range":
		return QueryTypeEnum_Range, nil
	case "Exist":
		return QueryTypeEnum_Exist, nil
	case "NotExist":
		return QueryTypeEnum_NotExist, nil
	}
	return QueryTypeEnum(0), fmt.Errorf("not a valid QueryTypeEnum string")
}

func QueryTypeEnumPtr(v QueryTypeEnum) *QueryTypeEnum { return &v }
func (p *QueryTypeEnum) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = QueryTypeEnum(result.Int64)
	return
}

func (p *QueryTypeEnum) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type SpanStatus int64

const (
	SpanStatus_Unknown SpanStatus = 0
	SpanStatus_Success SpanStatus = 1
	SpanStatus_Fail    SpanStatus = 2
)

func (p SpanStatus) String() string {
	switch p {
	case SpanStatus_Unknown:
		return "Unknown"
	case SpanStatus_Success:
		return "Success"
	case SpanStatus_Fail:
		return "Fail"
	}
	return "<UNSET>"
}

func SpanStatusFromString(s string) (SpanStatus, error) {
	switch s {
	case "Unknown":
		return SpanStatus_Unknown, nil
	case "Success":
		return SpanStatus_Success, nil
	case "Fail":
		return SpanStatus_Fail, nil
	}
	return SpanStatus(0), fmt.Errorf("not a valid SpanStatus string")
}

func SpanStatusPtr(v SpanStatus) *SpanStatus { return &v }
func (p *SpanStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SpanStatus(result.Int64)
	return
}

func (p *SpanStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type TagType int64

const (
	TagType_STRING TagType = 0
	TagType_DOUBLE TagType = 1
	TagType_BOOL   TagType = 2
	TagType_LONG   TagType = 3
	TagType_BYTES  TagType = 4
)

func (p TagType) String() string {
	switch p {
	case TagType_STRING:
		return "STRING"
	case TagType_DOUBLE:
		return "DOUBLE"
	case TagType_BOOL:
		return "BOOL"
	case TagType_LONG:
		return "LONG"
	case TagType_BYTES:
		return "BYTES"
	}
	return "<UNSET>"
}

func TagTypeFromString(s string) (TagType, error) {
	switch s {
	case "STRING":
		return TagType_STRING, nil
	case "DOUBLE":
		return TagType_DOUBLE, nil
	case "BOOL":
		return TagType_BOOL, nil
	case "LONG":
		return TagType_LONG, nil
	case "BYTES":
		return TagType_BYTES, nil
	}
	return TagType(0), fmt.Errorf("not a valid TagType string")
}

func TagTypePtr(v TagType) *TagType { return &v }
func (p *TagType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TagType(result.Int64)
	return
}

func (p *TagType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type QueryScene int64

const (
	// Doubao cici full link debugging station
	QueryScene_ALICE_OP QueryScene = 0
	// Doubao cici debugging function
	QueryScene_DOUBAO_CICI_DEBUG QueryScene = 1
	// Workflow debugging
	QueryScene_WORKFLOW_DEBUG QueryScene = 2
)

func (p QueryScene) String() string {
	switch p {
	case QueryScene_ALICE_OP:
		return "ALICE_OP"
	case QueryScene_DOUBAO_CICI_DEBUG:
		return "DOUBAO_CICI_DEBUG"
	case QueryScene_WORKFLOW_DEBUG:
		return "WORKFLOW_DEBUG"
	}
	return "<UNSET>"
}

func QuerySceneFromString(s string) (QueryScene, error) {
	switch s {
	case "ALICE_OP":
		return QueryScene_ALICE_OP, nil
	case "DOUBAO_CICI_DEBUG":
		return QueryScene_DOUBAO_CICI_DEBUG, nil
	case "WORKFLOW_DEBUG":
		return QueryScene_WORKFLOW_DEBUG, nil
	}
	return QueryScene(0), fmt.Errorf("not a valid QueryScene string")
}

func QueryScenePtr(v QueryScene) *QueryScene { return &v }
func (p *QueryScene) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = QueryScene(result.Int64)
	return
}

func (p *QueryScene) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type TenantLevel int64

const (
	TenantLevel_Ordinary          TenantLevel = 0
	TenantLevel_AdvancedWhitelist TenantLevel = 1
)

func (p TenantLevel) String() string {
	switch p {
	case TenantLevel_Ordinary:
		return "Ordinary"
	case TenantLevel_AdvancedWhitelist:
		return "AdvancedWhitelist"
	}
	return "<UNSET>"
}

func TenantLevelFromString(s string) (TenantLevel, error) {
	switch s {
	case "Ordinary":
		return TenantLevel_Ordinary, nil
	case "AdvancedWhitelist":
		return TenantLevel_AdvancedWhitelist, nil
	}
	return TenantLevel(0), fmt.Errorf("not a valid TenantLevel string")
}

func TenantLevelPtr(v TenantLevel) *TenantLevel { return &v }
func (p *TenantLevel) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TenantLevel(result.Int64)
	return
}

func (p *TenantLevel) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type InputOutputType int64

const (
	// Text type
	InputOutputType_TEXT InputOutputType = 0
)

func (p InputOutputType) String() string {
	switch p {
	case InputOutputType_TEXT:
		return "TEXT"
	}
	return "<UNSET>"
}

func InputOutputTypeFromString(s string) (InputOutputType, error) {
	switch s {
	case "TEXT":
		return InputOutputType_TEXT, nil
	}
	return InputOutputType(0), fmt.Errorf("not a valid InputOutputType string")
}

func InputOutputTypePtr(v InputOutputType) *InputOutputType { return &v }
func (p *InputOutputType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = InputOutputType(result.Int64)
	return
}

func (p *InputOutputType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// Tag
type TraceTag struct {
	Key     string  `thrift:"Key,1" json:"key" form:"Key" query:"Key"`
	TagType TagType `thrift:"TagType,2" json:"tag_type" form:"TagType" query:"TagType"`
	Value   *Value  `thrift:"Value,3" json:"value" form:"Value" query:"Value"`
}

func NewTraceTag() *TraceTag {
	return &TraceTag{}
}

func (p *TraceTag) InitDefault() {
}

func (p *TraceTag) GetKey() (v string) {
	return p.Key
}

func (p *TraceTag) GetTagType() (v TagType) {
	return p.TagType
}

var TraceTag_Value_DEFAULT *Value

func (p *TraceTag) GetValue() (v *Value) {
	if !p.IsSetValue() {
		return TraceTag_Value_DEFAULT
	}
	return p.Value
}

var fieldIDToName_TraceTag = map[int16]string{
	1: "Key",
	2: "TagType",
	3: "Value",
}

func (p *TraceTag) IsSetValue() bool {
	return p.Value != nil
}

func (p *TraceTag) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TraceTag[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TraceTag) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Key = _field
	return nil
}
func (p *TraceTag) ReadField2(iprot thrift.TProtocol) error {

	var _field TagType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TagType(v)
	}
	p.TagType = _field
	return nil
}
func (p *TraceTag) ReadField3(iprot thrift.TProtocol) error {
	_field := NewValue()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Value = _field
	return nil
}

func (p *TraceTag) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TraceTag"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TraceTag) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Key", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Key); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TraceTag) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TagType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TagType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *TraceTag) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Value", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Value.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *TraceTag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TraceTag(%+v)", *p)

}

type FilterTag struct {
	DataType     *string        `thrift:"DataType,1,optional" json:"data_type" form:"DataType" query:"DataType"`
	TagKey       *string        `thrift:"TagKey,2,optional" json:"tag_key" form:"TagKey" query:"TagKey"`
	MultiTagKeys []string       `thrift:"MultiTagKeys,3,optional" json:"multi_tag_keys" form:"MultiTagKeys" query:"MultiTagKeys"`
	Values       []string       `thrift:"Values,4,optional" json:"values" form:"Values" query:"Values"`
	QueryType    *QueryTypeEnum `thrift:"QueryType,5,optional" json:"query_type" form:"QueryType" query:"QueryType"`
}

func NewFilterTag() *FilterTag {
	return &FilterTag{}
}

func (p *FilterTag) InitDefault() {
}

var FilterTag_DataType_DEFAULT string

func (p *FilterTag) GetDataType() (v string) {
	if !p.IsSetDataType() {
		return FilterTag_DataType_DEFAULT
	}
	return *p.DataType
}

var FilterTag_TagKey_DEFAULT string

func (p *FilterTag) GetTagKey() (v string) {
	if !p.IsSetTagKey() {
		return FilterTag_TagKey_DEFAULT
	}
	return *p.TagKey
}

var FilterTag_MultiTagKeys_DEFAULT []string

func (p *FilterTag) GetMultiTagKeys() (v []string) {
	if !p.IsSetMultiTagKeys() {
		return FilterTag_MultiTagKeys_DEFAULT
	}
	return p.MultiTagKeys
}

var FilterTag_Values_DEFAULT []string

func (p *FilterTag) GetValues() (v []string) {
	if !p.IsSetValues() {
		return FilterTag_Values_DEFAULT
	}
	return p.Values
}

var FilterTag_QueryType_DEFAULT QueryTypeEnum

func (p *FilterTag) GetQueryType() (v QueryTypeEnum) {
	if !p.IsSetQueryType() {
		return FilterTag_QueryType_DEFAULT
	}
	return *p.QueryType
}

var fieldIDToName_FilterTag = map[int16]string{
	1: "DataType",
	2: "TagKey",
	3: "MultiTagKeys",
	4: "Values",
	5: "QueryType",
}

func (p *FilterTag) IsSetDataType() bool {
	return p.DataType != nil
}

func (p *FilterTag) IsSetTagKey() bool {
	return p.TagKey != nil
}

func (p *FilterTag) IsSetMultiTagKeys() bool {
	return p.MultiTagKeys != nil
}

func (p *FilterTag) IsSetValues() bool {
	return p.Values != nil
}

func (p *FilterTag) IsSetQueryType() bool {
	return p.QueryType != nil
}

func (p *FilterTag) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FilterTag[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FilterTag) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DataType = _field
	return nil
}
func (p *FilterTag) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TagKey = _field
	return nil
}
func (p *FilterTag) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MultiTagKeys = _field
	return nil
}
func (p *FilterTag) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Values = _field
	return nil
}
func (p *FilterTag) ReadField5(iprot thrift.TProtocol) error {

	var _field *QueryTypeEnum
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := QueryTypeEnum(v)
		_field = &tmp
	}
	p.QueryType = _field
	return nil
}

func (p *FilterTag) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FilterTag"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FilterTag) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetDataType() {
		if err = oprot.WriteFieldBegin("DataType", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DataType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FilterTag) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTagKey() {
		if err = oprot.WriteFieldBegin("TagKey", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TagKey); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *FilterTag) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetMultiTagKeys() {
		if err = oprot.WriteFieldBegin("MultiTagKeys", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.MultiTagKeys)); err != nil {
			return err
		}
		for _, v := range p.MultiTagKeys {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *FilterTag) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetValues() {
		if err = oprot.WriteFieldBegin("Values", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Values)); err != nil {
			return err
		}
		for _, v := range p.Values {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *FilterTag) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetQueryType() {
		if err = oprot.WriteFieldBegin("QueryType", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.QueryType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *FilterTag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FilterTag(%+v)", *p)

}

type ListRootSpansRequest struct {
	// It's in milliseconds.
	StartAt int64 `thrift:"StartAt,2,required" json:"start_at" form:"start_at,required" `
	// It's in milliseconds.
	EndAt           int64       `thrift:"EndAt,3,required" json:"end_at" form:"end_at,required" `
	Limit           *int16      `thrift:"Limit,4,optional" json:"limit" form:"limit" `
	DescByStartTime *bool       `thrift:"DescByStartTime,5,optional" json:"desc_by_start_time" form:"desc_by_start_time" `
	Offset          *int32      `thrift:"Offset,6,optional" json:"offset" form:"offset" `
	WorkflowID      string      `thrift:"WorkflowID,7,required" json:"workflow_id" form:"workflow_id,required" `
	Input           *string     `thrift:"Input,8,optional" json:"input" form:"input" `
	Status          *SpanStatus `thrift:"Status,9,optional" json:"status" form:"status" `
	// Formal run/practice run/Node Debug
	ExecuteMode *int32     `thrift:"ExecuteMode,10,optional" json:"execute_mode" form:"execute_mode" `
	Base        *base.Base `thrift:"Base,255,optional" form:"Base" json:"Base,omitempty" query:"Base"`
}

func NewListRootSpansRequest() *ListRootSpansRequest {
	return &ListRootSpansRequest{}
}

func (p *ListRootSpansRequest) InitDefault() {
}

func (p *ListRootSpansRequest) GetStartAt() (v int64) {
	return p.StartAt
}

func (p *ListRootSpansRequest) GetEndAt() (v int64) {
	return p.EndAt
}

var ListRootSpansRequest_Limit_DEFAULT int16

func (p *ListRootSpansRequest) GetLimit() (v int16) {
	if !p.IsSetLimit() {
		return ListRootSpansRequest_Limit_DEFAULT
	}
	return *p.Limit
}

var ListRootSpansRequest_DescByStartTime_DEFAULT bool

func (p *ListRootSpansRequest) GetDescByStartTime() (v bool) {
	if !p.IsSetDescByStartTime() {
		return ListRootSpansRequest_DescByStartTime_DEFAULT
	}
	return *p.DescByStartTime
}

var ListRootSpansRequest_Offset_DEFAULT int32

func (p *ListRootSpansRequest) GetOffset() (v int32) {
	if !p.IsSetOffset() {
		return ListRootSpansRequest_Offset_DEFAULT
	}
	return *p.Offset
}

func (p *ListRootSpansRequest) GetWorkflowID() (v string) {
	return p.WorkflowID
}

var ListRootSpansRequest_Input_DEFAULT string

func (p *ListRootSpansRequest) GetInput() (v string) {
	if !p.IsSetInput() {
		return ListRootSpansRequest_Input_DEFAULT
	}
	return *p.Input
}

var ListRootSpansRequest_Status_DEFAULT SpanStatus

func (p *ListRootSpansRequest) GetStatus() (v SpanStatus) {
	if !p.IsSetStatus() {
		return ListRootSpansRequest_Status_DEFAULT
	}
	return *p.Status
}

var ListRootSpansRequest_ExecuteMode_DEFAULT int32

func (p *ListRootSpansRequest) GetExecuteMode() (v int32) {
	if !p.IsSetExecuteMode() {
		return ListRootSpansRequest_ExecuteMode_DEFAULT
	}
	return *p.ExecuteMode
}

var ListRootSpansRequest_Base_DEFAULT *base.Base

func (p *ListRootSpansRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ListRootSpansRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_ListRootSpansRequest = map[int16]string{
	2:   "StartAt",
	3:   "EndAt",
	4:   "Limit",
	5:   "DescByStartTime",
	6:   "Offset",
	7:   "WorkflowID",
	8:   "Input",
	9:   "Status",
	10:  "ExecuteMode",
	255: "Base",
}

func (p *ListRootSpansRequest) IsSetLimit() bool {
	return p.Limit != nil
}

func (p *ListRootSpansRequest) IsSetDescByStartTime() bool {
	return p.DescByStartTime != nil
}

func (p *ListRootSpansRequest) IsSetOffset() bool {
	return p.Offset != nil
}

func (p *ListRootSpansRequest) IsSetInput() bool {
	return p.Input != nil
}

func (p *ListRootSpansRequest) IsSetStatus() bool {
	return p.Status != nil
}

func (p *ListRootSpansRequest) IsSetExecuteMode() bool {
	return p.ExecuteMode != nil
}

func (p *ListRootSpansRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *ListRootSpansRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetStartAt bool = false
	var issetEndAt bool = false
	var issetWorkflowID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartAt = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndAt = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I16 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetWorkflowID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetStartAt {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetEndAt {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetWorkflowID {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListRootSpansRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListRootSpansRequest[fieldId]))
}

func (p *ListRootSpansRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartAt = _field
	return nil
}
func (p *ListRootSpansRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndAt = _field
	return nil
}
func (p *ListRootSpansRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *int16
	if v, err := iprot.ReadI16(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Limit = _field
	return nil
}
func (p *ListRootSpansRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DescByStartTime = _field
	return nil
}
func (p *ListRootSpansRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Offset = _field
	return nil
}
func (p *ListRootSpansRequest) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkflowID = _field
	return nil
}
func (p *ListRootSpansRequest) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Input = _field
	return nil
}
func (p *ListRootSpansRequest) ReadField9(iprot thrift.TProtocol) error {

	var _field *SpanStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SpanStatus(v)
		_field = &tmp
	}
	p.Status = _field
	return nil
}
func (p *ListRootSpansRequest) ReadField10(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExecuteMode = _field
	return nil
}
func (p *ListRootSpansRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ListRootSpansRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListRootSpansRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListRootSpansRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartAt", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.StartAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ListRootSpansRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndAt", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.EndAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ListRootSpansRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetLimit() {
		if err = oprot.WriteFieldBegin("Limit", thrift.I16, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI16(*p.Limit); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ListRootSpansRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescByStartTime() {
		if err = oprot.WriteFieldBegin("DescByStartTime", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.DescByStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ListRootSpansRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetOffset() {
		if err = oprot.WriteFieldBegin("Offset", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Offset); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ListRootSpansRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WorkflowID", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WorkflowID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *ListRootSpansRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetInput() {
		if err = oprot.WriteFieldBegin("Input", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Input); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *ListRootSpansRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err = oprot.WriteFieldBegin("Status", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Status)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *ListRootSpansRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecuteMode() {
		if err = oprot.WriteFieldBegin("ExecuteMode", thrift.I32, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ExecuteMode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *ListRootSpansRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ListRootSpansRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListRootSpansRequest(%+v)", *p)

}

type Span struct {
	TraceID  string `thrift:"TraceID,1" json:"trace_id" form:"TraceID" query:"TraceID"`
	LogID    string `thrift:"LogID,2" json:"log_id" form:"LogID" query:"LogID"`
	PSM      string `thrift:"PSM,3" json:"psm" form:"PSM" query:"PSM"`
	DC       string `thrift:"DC,4" json:"dc" form:"DC" query:"DC"`
	PodName  string `thrift:"PodName,5" json:"pod_name" form:"PodName" query:"PodName"`
	SpanID   string `thrift:"SpanID,6" json:"span_id" form:"SpanID" query:"SpanID"`
	Type     string `thrift:"Type,7" json:"type" form:"Type" query:"Type"`
	Name     string `thrift:"Name,8" json:"name" form:"Name" query:"Name"`
	ParentID string `thrift:"ParentID,9" json:"parent_id" form:"ParentID" query:"ParentID"`
	// It's in milliseconds.
	Duration int64 `thrift:"Duration,10" json:"duration" form:"Duration" query:"Duration"`
	// It's in milliseconds.
	StartTime  int64       `thrift:"StartTime,11" json:"start_time" form:"StartTime" query:"StartTime"`
	StatusCode int32       `thrift:"StatusCode,12" json:"status_code" form:"StatusCode" query:"StatusCode"`
	Tags       []*TraceTag `thrift:"Tags,13" json:"tags" form:"Tags" query:"Tags"`
}

func NewSpan() *Span {
	return &Span{}
}

func (p *Span) InitDefault() {
}

func (p *Span) GetTraceID() (v string) {
	return p.TraceID
}

func (p *Span) GetLogID() (v string) {
	return p.LogID
}

func (p *Span) GetPSM() (v string) {
	return p.PSM
}

func (p *Span) GetDC() (v string) {
	return p.DC
}

func (p *Span) GetPodName() (v string) {
	return p.PodName
}

func (p *Span) GetSpanID() (v string) {
	return p.SpanID
}

func (p *Span) GetType() (v string) {
	return p.Type
}

func (p *Span) GetName() (v string) {
	return p.Name
}

func (p *Span) GetParentID() (v string) {
	return p.ParentID
}

func (p *Span) GetDuration() (v int64) {
	return p.Duration
}

func (p *Span) GetStartTime() (v int64) {
	return p.StartTime
}

func (p *Span) GetStatusCode() (v int32) {
	return p.StatusCode
}

func (p *Span) GetTags() (v []*TraceTag) {
	return p.Tags
}

var fieldIDToName_Span = map[int16]string{
	1:  "TraceID",
	2:  "LogID",
	3:  "PSM",
	4:  "DC",
	5:  "PodName",
	6:  "SpanID",
	7:  "Type",
	8:  "Name",
	9:  "ParentID",
	10: "Duration",
	11: "StartTime",
	12: "StatusCode",
	13: "Tags",
}

func (p *Span) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Span[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Span) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TraceID = _field
	return nil
}
func (p *Span) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LogID = _field
	return nil
}
func (p *Span) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PSM = _field
	return nil
}
func (p *Span) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DC = _field
	return nil
}
func (p *Span) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PodName = _field
	return nil
}
func (p *Span) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SpanID = _field
	return nil
}
func (p *Span) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}
func (p *Span) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *Span) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ParentID = _field
	return nil
}
func (p *Span) ReadField10(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Duration = _field
	return nil
}
func (p *Span) ReadField11(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *Span) ReadField12(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StatusCode = _field
	return nil
}
func (p *Span) ReadField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TraceTag, 0, size)
	values := make([]TraceTag, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tags = _field
	return nil
}

func (p *Span) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Span"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Span) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TraceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TraceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Span) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LogID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Span) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PSM", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PSM); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Span) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DC", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DC); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Span) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PodName", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PodName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *Span) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SpanID", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SpanID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *Span) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Type", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *Span) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *Span) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ParentID", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ParentID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *Span) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Duration", thrift.I64, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Duration); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *Span) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *Span) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StatusCode", thrift.I32, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StatusCode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *Span) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Tags", thrift.LIST, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tags)); err != nil {
		return err
	}
	for _, v := range p.Tags {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *Span) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Span(%+v)", *p)

}

type Value struct {
	VStr    *string  `thrift:"VStr,1,optional" json:"v_str" form:"VStr" query:"VStr"`
	VDouble *float64 `thrift:"VDouble,2,optional" json:"v_double" form:"VDouble" query:"VDouble"`
	VBool   *bool    `thrift:"VBool,3,optional" json:"v_bool" form:"VBool" query:"VBool"`
	VLong   *int64   `thrift:"VLong,4,optional" json:"v_long" form:"VLong" query:"VLong"`
	VBytes  []byte   `thrift:"VBytes,5,optional" json:"v_bytes" form:"VBytes" query:"VBytes"`
}

func NewValue() *Value {
	return &Value{}
}

func (p *Value) InitDefault() {
}

var Value_VStr_DEFAULT string

func (p *Value) GetVStr() (v string) {
	if !p.IsSetVStr() {
		return Value_VStr_DEFAULT
	}
	return *p.VStr
}

var Value_VDouble_DEFAULT float64

func (p *Value) GetVDouble() (v float64) {
	if !p.IsSetVDouble() {
		return Value_VDouble_DEFAULT
	}
	return *p.VDouble
}

var Value_VBool_DEFAULT bool

func (p *Value) GetVBool() (v bool) {
	if !p.IsSetVBool() {
		return Value_VBool_DEFAULT
	}
	return *p.VBool
}

var Value_VLong_DEFAULT int64

func (p *Value) GetVLong() (v int64) {
	if !p.IsSetVLong() {
		return Value_VLong_DEFAULT
	}
	return *p.VLong
}

var Value_VBytes_DEFAULT []byte

func (p *Value) GetVBytes() (v []byte) {
	if !p.IsSetVBytes() {
		return Value_VBytes_DEFAULT
	}
	return p.VBytes
}

var fieldIDToName_Value = map[int16]string{
	1: "VStr",
	2: "VDouble",
	3: "VBool",
	4: "VLong",
	5: "VBytes",
}

func (p *Value) IsSetVStr() bool {
	return p.VStr != nil
}

func (p *Value) IsSetVDouble() bool {
	return p.VDouble != nil
}

func (p *Value) IsSetVBool() bool {
	return p.VBool != nil
}

func (p *Value) IsSetVLong() bool {
	return p.VLong != nil
}

func (p *Value) IsSetVBytes() bool {
	return p.VBytes != nil
}

func (p *Value) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Value[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Value) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.VStr = _field
	return nil
}
func (p *Value) ReadField2(iprot thrift.TProtocol) error {

	var _field *float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.VDouble = _field
	return nil
}
func (p *Value) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.VBool = _field
	return nil
}
func (p *Value) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.VLong = _field
	return nil
}
func (p *Value) ReadField5(iprot thrift.TProtocol) error {

	var _field []byte
	if v, err := iprot.ReadBinary(); err != nil {
		return err
	} else {
		_field = []byte(v)
	}
	p.VBytes = _field
	return nil
}

func (p *Value) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Value"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Value) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetVStr() {
		if err = oprot.WriteFieldBegin("VStr", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.VStr); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Value) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetVDouble() {
		if err = oprot.WriteFieldBegin("VDouble", thrift.DOUBLE, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteDouble(*p.VDouble); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Value) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetVBool() {
		if err = oprot.WriteFieldBegin("VBool", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.VBool); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Value) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetVLong() {
		if err = oprot.WriteFieldBegin("VLong", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.VLong); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Value) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetVBytes() {
		if err = oprot.WriteFieldBegin("VBytes", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBinary([]byte(p.VBytes)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *Value) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Value(%+v)", *p)

}

type ListRootSpansResponse struct {
	Spans    []*Span        `thrift:"Spans,1,optional" json:"spans" form:"spans" `
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" form:"BaseResp" json:"BaseResp,omitempty" query:"BaseResp"`
}

func NewListRootSpansResponse() *ListRootSpansResponse {
	return &ListRootSpansResponse{}
}

func (p *ListRootSpansResponse) InitDefault() {
}

var ListRootSpansResponse_Spans_DEFAULT []*Span

func (p *ListRootSpansResponse) GetSpans() (v []*Span) {
	if !p.IsSetSpans() {
		return ListRootSpansResponse_Spans_DEFAULT
	}
	return p.Spans
}

var ListRootSpansResponse_BaseResp_DEFAULT *base.BaseResp

func (p *ListRootSpansResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ListRootSpansResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_ListRootSpansResponse = map[int16]string{
	1:   "Spans",
	255: "BaseResp",
}

func (p *ListRootSpansResponse) IsSetSpans() bool {
	return p.Spans != nil
}

func (p *ListRootSpansResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ListRootSpansResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListRootSpansResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ListRootSpansResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Span, 0, size)
	values := make([]Span, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Spans = _field
	return nil
}
func (p *ListRootSpansResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ListRootSpansResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListRootSpansResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListRootSpansResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSpans() {
		if err = oprot.WriteFieldBegin("Spans", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Spans)); err != nil {
			return err
		}
		for _, v := range p.Spans {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ListRootSpansResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ListRootSpansResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListRootSpansResponse(%+v)", *p)

}

type GetTraceSDKRequest struct {
	LogID *string `thrift:"LogID,2,optional" json:"log_id" query:"log_id" `
	// It's in milliseconds.
	StartAt *int64 `thrift:"StartAt,4,optional" json:"start_at" query:"start_at" `
	// It's in milliseconds.
	EndAt      *int64     `thrift:"EndAt,5,optional" json:"end_at" query:"end_at" `
	WorkflowID *int64     `thrift:"WorkflowID,6,optional" json:"workflow_id" query:"workflow_id" `
	ExecuteID  *int64     `thrift:"ExecuteID,7,optional" json:"execute_id" query:"execute_id" `
	Base       *base.Base `thrift:"Base,255,optional" form:"Base" json:"Base,omitempty" query:"Base"`
}

func NewGetTraceSDKRequest() *GetTraceSDKRequest {
	return &GetTraceSDKRequest{}
}

func (p *GetTraceSDKRequest) InitDefault() {
}

var GetTraceSDKRequest_LogID_DEFAULT string

func (p *GetTraceSDKRequest) GetLogID() (v string) {
	if !p.IsSetLogID() {
		return GetTraceSDKRequest_LogID_DEFAULT
	}
	return *p.LogID
}

var GetTraceSDKRequest_StartAt_DEFAULT int64

func (p *GetTraceSDKRequest) GetStartAt() (v int64) {
	if !p.IsSetStartAt() {
		return GetTraceSDKRequest_StartAt_DEFAULT
	}
	return *p.StartAt
}

var GetTraceSDKRequest_EndAt_DEFAULT int64

func (p *GetTraceSDKRequest) GetEndAt() (v int64) {
	if !p.IsSetEndAt() {
		return GetTraceSDKRequest_EndAt_DEFAULT
	}
	return *p.EndAt
}

var GetTraceSDKRequest_WorkflowID_DEFAULT int64

func (p *GetTraceSDKRequest) GetWorkflowID() (v int64) {
	if !p.IsSetWorkflowID() {
		return GetTraceSDKRequest_WorkflowID_DEFAULT
	}
	return *p.WorkflowID
}

var GetTraceSDKRequest_ExecuteID_DEFAULT int64

func (p *GetTraceSDKRequest) GetExecuteID() (v int64) {
	if !p.IsSetExecuteID() {
		return GetTraceSDKRequest_ExecuteID_DEFAULT
	}
	return *p.ExecuteID
}

var GetTraceSDKRequest_Base_DEFAULT *base.Base

func (p *GetTraceSDKRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetTraceSDKRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_GetTraceSDKRequest = map[int16]string{
	2:   "LogID",
	4:   "StartAt",
	5:   "EndAt",
	6:   "WorkflowID",
	7:   "ExecuteID",
	255: "Base",
}

func (p *GetTraceSDKRequest) IsSetLogID() bool {
	return p.LogID != nil
}

func (p *GetTraceSDKRequest) IsSetStartAt() bool {
	return p.StartAt != nil
}

func (p *GetTraceSDKRequest) IsSetEndAt() bool {
	return p.EndAt != nil
}

func (p *GetTraceSDKRequest) IsSetWorkflowID() bool {
	return p.WorkflowID != nil
}

func (p *GetTraceSDKRequest) IsSetExecuteID() bool {
	return p.ExecuteID != nil
}

func (p *GetTraceSDKRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetTraceSDKRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetTraceSDKRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetTraceSDKRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.LogID = _field
	return nil
}
func (p *GetTraceSDKRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StartAt = _field
	return nil
}
func (p *GetTraceSDKRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndAt = _field
	return nil
}
func (p *GetTraceSDKRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.WorkflowID = _field
	return nil
}
func (p *GetTraceSDKRequest) ReadField7(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExecuteID = _field
	return nil
}
func (p *GetTraceSDKRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetTraceSDKRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetTraceSDKRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetTraceSDKRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetLogID() {
		if err = oprot.WriteFieldBegin("LogID", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.LogID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetTraceSDKRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStartAt() {
		if err = oprot.WriteFieldBegin("StartAt", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.StartAt); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GetTraceSDKRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndAt() {
		if err = oprot.WriteFieldBegin("EndAt", thrift.I64, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.EndAt); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *GetTraceSDKRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetWorkflowID() {
		if err = oprot.WriteFieldBegin("WorkflowID", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.WorkflowID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *GetTraceSDKRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecuteID() {
		if err = oprot.WriteFieldBegin("ExecuteID", thrift.I64, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ExecuteID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *GetTraceSDKRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetTraceSDKRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTraceSDKRequest(%+v)", *p)

}

type GetTraceSDKResponse struct {
	Data     *TraceFrontend `thrift:"data,1,optional" form:"data" json:"data,omitempty" query:"data"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" form:"BaseResp" json:"BaseResp,omitempty" query:"BaseResp"`
}

func NewGetTraceSDKResponse() *GetTraceSDKResponse {
	return &GetTraceSDKResponse{}
}

func (p *GetTraceSDKResponse) InitDefault() {
}

var GetTraceSDKResponse_Data_DEFAULT *TraceFrontend

func (p *GetTraceSDKResponse) GetData() (v *TraceFrontend) {
	if !p.IsSetData() {
		return GetTraceSDKResponse_Data_DEFAULT
	}
	return p.Data
}

var GetTraceSDKResponse_BaseResp_DEFAULT *base.BaseResp

func (p *GetTraceSDKResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetTraceSDKResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_GetTraceSDKResponse = map[int16]string{
	1:   "data",
	255: "BaseResp",
}

func (p *GetTraceSDKResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *GetTraceSDKResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetTraceSDKResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetTraceSDKResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetTraceSDKResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewTraceFrontend()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *GetTraceSDKResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetTraceSDKResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetTraceSDKResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetTraceSDKResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetData() {
		if err = oprot.WriteFieldBegin("data", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Data.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetTraceSDKResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetTraceSDKResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTraceSDKResponse(%+v)", *p)

}

type KeyScene struct {
	// Scenarios such as "Split search terms"\ "Search"
	Scene *string `thrift:"Scene,1,optional" json:"scene" form:"Scene" query:"Scene"`
	// status information
	StatusMessage *string `thrift:"StatusMessage,2,optional" json:"status_message" form:"StatusMessage" query:"StatusMessage"`
	System        *string `thrift:"System,3,optional" json:"system" form:"System" query:"System"`
	// chat history
	HistoryMessages []*MessageItem `thrift:"HistoryMessages,4,optional" json:"history_messages" form:"HistoryMessages" query:"HistoryMessages"`
	// input
	Input *KeySceneInput `thrift:"Input,5,optional" json:"input" form:"Input" query:"Input"`
	// output
	Output *KeySceneOutput `thrift:"Output,6,optional" json:"output" form:"Output" query:"Output"`
	// It's in milliseconds.
	Duration *int64 `thrift:"Duration,7,optional" json:"duration" form:"Duration" query:"Duration"`
	// Start time, used for sorting, in milliseconds
	StartTime *int64 `thrift:"StartTime,8,optional" json:"start_time" form:"start_time" `
	// subscene
	SubKeyScenes []*KeyScene `thrift:"SubKeyScenes,9,optional" json:"sub_key_scenes" form:"SubKeyScenes" query:"SubKeyScenes"`
}

func NewKeyScene() *KeyScene {
	return &KeyScene{}
}

func (p *KeyScene) InitDefault() {
}

var KeyScene_Scene_DEFAULT string

func (p *KeyScene) GetScene() (v string) {
	if !p.IsSetScene() {
		return KeyScene_Scene_DEFAULT
	}
	return *p.Scene
}

var KeyScene_StatusMessage_DEFAULT string

func (p *KeyScene) GetStatusMessage() (v string) {
	if !p.IsSetStatusMessage() {
		return KeyScene_StatusMessage_DEFAULT
	}
	return *p.StatusMessage
}

var KeyScene_System_DEFAULT string

func (p *KeyScene) GetSystem() (v string) {
	if !p.IsSetSystem() {
		return KeyScene_System_DEFAULT
	}
	return *p.System
}

var KeyScene_HistoryMessages_DEFAULT []*MessageItem

func (p *KeyScene) GetHistoryMessages() (v []*MessageItem) {
	if !p.IsSetHistoryMessages() {
		return KeyScene_HistoryMessages_DEFAULT
	}
	return p.HistoryMessages
}

var KeyScene_Input_DEFAULT *KeySceneInput

func (p *KeyScene) GetInput() (v *KeySceneInput) {
	if !p.IsSetInput() {
		return KeyScene_Input_DEFAULT
	}
	return p.Input
}

var KeyScene_Output_DEFAULT *KeySceneOutput

func (p *KeyScene) GetOutput() (v *KeySceneOutput) {
	if !p.IsSetOutput() {
		return KeyScene_Output_DEFAULT
	}
	return p.Output
}

var KeyScene_Duration_DEFAULT int64

func (p *KeyScene) GetDuration() (v int64) {
	if !p.IsSetDuration() {
		return KeyScene_Duration_DEFAULT
	}
	return *p.Duration
}

var KeyScene_StartTime_DEFAULT int64

func (p *KeyScene) GetStartTime() (v int64) {
	if !p.IsSetStartTime() {
		return KeyScene_StartTime_DEFAULT
	}
	return *p.StartTime
}

var KeyScene_SubKeyScenes_DEFAULT []*KeyScene

func (p *KeyScene) GetSubKeyScenes() (v []*KeyScene) {
	if !p.IsSetSubKeyScenes() {
		return KeyScene_SubKeyScenes_DEFAULT
	}
	return p.SubKeyScenes
}

var fieldIDToName_KeyScene = map[int16]string{
	1: "Scene",
	2: "StatusMessage",
	3: "System",
	4: "HistoryMessages",
	5: "Input",
	6: "Output",
	7: "Duration",
	8: "StartTime",
	9: "SubKeyScenes",
}

func (p *KeyScene) IsSetScene() bool {
	return p.Scene != nil
}

func (p *KeyScene) IsSetStatusMessage() bool {
	return p.StatusMessage != nil
}

func (p *KeyScene) IsSetSystem() bool {
	return p.System != nil
}

func (p *KeyScene) IsSetHistoryMessages() bool {
	return p.HistoryMessages != nil
}

func (p *KeyScene) IsSetInput() bool {
	return p.Input != nil
}

func (p *KeyScene) IsSetOutput() bool {
	return p.Output != nil
}

func (p *KeyScene) IsSetDuration() bool {
	return p.Duration != nil
}

func (p *KeyScene) IsSetStartTime() bool {
	return p.StartTime != nil
}

func (p *KeyScene) IsSetSubKeyScenes() bool {
	return p.SubKeyScenes != nil
}

func (p *KeyScene) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_KeyScene[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *KeyScene) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Scene = _field
	return nil
}
func (p *KeyScene) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StatusMessage = _field
	return nil
}
func (p *KeyScene) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.System = _field
	return nil
}
func (p *KeyScene) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*MessageItem, 0, size)
	values := make([]MessageItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.HistoryMessages = _field
	return nil
}
func (p *KeyScene) ReadField5(iprot thrift.TProtocol) error {
	_field := NewKeySceneInput()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Input = _field
	return nil
}
func (p *KeyScene) ReadField6(iprot thrift.TProtocol) error {
	_field := NewKeySceneOutput()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Output = _field
	return nil
}
func (p *KeyScene) ReadField7(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Duration = _field
	return nil
}
func (p *KeyScene) ReadField8(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StartTime = _field
	return nil
}
func (p *KeyScene) ReadField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*KeyScene, 0, size)
	values := make([]KeyScene, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SubKeyScenes = _field
	return nil
}

func (p *KeyScene) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("KeyScene"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *KeyScene) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetScene() {
		if err = oprot.WriteFieldBegin("Scene", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Scene); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *KeyScene) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatusMessage() {
		if err = oprot.WriteFieldBegin("StatusMessage", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.StatusMessage); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *KeyScene) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSystem() {
		if err = oprot.WriteFieldBegin("System", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.System); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *KeyScene) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetHistoryMessages() {
		if err = oprot.WriteFieldBegin("HistoryMessages", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.HistoryMessages)); err != nil {
			return err
		}
		for _, v := range p.HistoryMessages {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *KeyScene) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetInput() {
		if err = oprot.WriteFieldBegin("Input", thrift.STRUCT, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Input.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *KeyScene) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetOutput() {
		if err = oprot.WriteFieldBegin("Output", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Output.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *KeyScene) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetDuration() {
		if err = oprot.WriteFieldBegin("Duration", thrift.I64, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Duration); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *KeyScene) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetStartTime() {
		if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.StartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *KeyScene) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetSubKeyScenes() {
		if err = oprot.WriteFieldBegin("SubKeyScenes", thrift.LIST, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SubKeyScenes)); err != nil {
			return err
		}
		for _, v := range p.SubKeyScenes {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *KeyScene) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KeyScene(%+v)", *p)

}

type KeySceneInput struct {
	Role        *string                `thrift:"Role,1,optional" json:"role" form:"Role" query:"Role"`
	ContentList []*TraceSummaryContent `thrift:"contentList,2,optional" json:"content_list" form:"contentList" query:"contentList"`
}

func NewKeySceneInput() *KeySceneInput {
	return &KeySceneInput{}
}

func (p *KeySceneInput) InitDefault() {
}

var KeySceneInput_Role_DEFAULT string

func (p *KeySceneInput) GetRole() (v string) {
	if !p.IsSetRole() {
		return KeySceneInput_Role_DEFAULT
	}
	return *p.Role
}

var KeySceneInput_ContentList_DEFAULT []*TraceSummaryContent

func (p *KeySceneInput) GetContentList() (v []*TraceSummaryContent) {
	if !p.IsSetContentList() {
		return KeySceneInput_ContentList_DEFAULT
	}
	return p.ContentList
}

var fieldIDToName_KeySceneInput = map[int16]string{
	1: "Role",
	2: "contentList",
}

func (p *KeySceneInput) IsSetRole() bool {
	return p.Role != nil
}

func (p *KeySceneInput) IsSetContentList() bool {
	return p.ContentList != nil
}

func (p *KeySceneInput) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_KeySceneInput[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *KeySceneInput) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Role = _field
	return nil
}
func (p *KeySceneInput) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TraceSummaryContent, 0, size)
	values := make([]TraceSummaryContent, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ContentList = _field
	return nil
}

func (p *KeySceneInput) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("KeySceneInput"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *KeySceneInput) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetRole() {
		if err = oprot.WriteFieldBegin("Role", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Role); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *KeySceneInput) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetContentList() {
		if err = oprot.WriteFieldBegin("contentList", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ContentList)); err != nil {
			return err
		}
		for _, v := range p.ContentList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *KeySceneInput) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KeySceneInput(%+v)", *p)

}

type KeySceneOutput struct {
	Role        *string                `thrift:"Role,1,optional" json:"role" form:"Role" query:"Role"`
	ContentList []*TraceSummaryContent `thrift:"contentList,2,optional" json:"content_list" form:"contentList" query:"contentList"`
}

func NewKeySceneOutput() *KeySceneOutput {
	return &KeySceneOutput{}
}

func (p *KeySceneOutput) InitDefault() {
}

var KeySceneOutput_Role_DEFAULT string

func (p *KeySceneOutput) GetRole() (v string) {
	if !p.IsSetRole() {
		return KeySceneOutput_Role_DEFAULT
	}
	return *p.Role
}

var KeySceneOutput_ContentList_DEFAULT []*TraceSummaryContent

func (p *KeySceneOutput) GetContentList() (v []*TraceSummaryContent) {
	if !p.IsSetContentList() {
		return KeySceneOutput_ContentList_DEFAULT
	}
	return p.ContentList
}

var fieldIDToName_KeySceneOutput = map[int16]string{
	1: "Role",
	2: "contentList",
}

func (p *KeySceneOutput) IsSetRole() bool {
	return p.Role != nil
}

func (p *KeySceneOutput) IsSetContentList() bool {
	return p.ContentList != nil
}

func (p *KeySceneOutput) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_KeySceneOutput[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *KeySceneOutput) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Role = _field
	return nil
}
func (p *KeySceneOutput) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TraceSummaryContent, 0, size)
	values := make([]TraceSummaryContent, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ContentList = _field
	return nil
}

func (p *KeySceneOutput) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("KeySceneOutput"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *KeySceneOutput) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetRole() {
		if err = oprot.WriteFieldBegin("Role", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Role); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *KeySceneOutput) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetContentList() {
		if err = oprot.WriteFieldBegin("contentList", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ContentList)); err != nil {
			return err
		}
		for _, v := range p.ContentList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *KeySceneOutput) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KeySceneOutput(%+v)", *p)

}

type TraceSummaryContent struct {
	// key
	Key *string `thrift:"Key,1,optional" json:"key" form:"Key" query:"Key"`
	// content
	Content *string `thrift:"Content,2,optional" json:"content" form:"Content" query:"Content"`
}

func NewTraceSummaryContent() *TraceSummaryContent {
	return &TraceSummaryContent{}
}

func (p *TraceSummaryContent) InitDefault() {
}

var TraceSummaryContent_Key_DEFAULT string

func (p *TraceSummaryContent) GetKey() (v string) {
	if !p.IsSetKey() {
		return TraceSummaryContent_Key_DEFAULT
	}
	return *p.Key
}

var TraceSummaryContent_Content_DEFAULT string

func (p *TraceSummaryContent) GetContent() (v string) {
	if !p.IsSetContent() {
		return TraceSummaryContent_Content_DEFAULT
	}
	return *p.Content
}

var fieldIDToName_TraceSummaryContent = map[int16]string{
	1: "Key",
	2: "Content",
}

func (p *TraceSummaryContent) IsSetKey() bool {
	return p.Key != nil
}

func (p *TraceSummaryContent) IsSetContent() bool {
	return p.Content != nil
}

func (p *TraceSummaryContent) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TraceSummaryContent[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TraceSummaryContent) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Key = _field
	return nil
}
func (p *TraceSummaryContent) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Content = _field
	return nil
}

func (p *TraceSummaryContent) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TraceSummaryContent"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TraceSummaryContent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetKey() {
		if err = oprot.WriteFieldBegin("Key", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Key); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TraceSummaryContent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetContent() {
		if err = oprot.WriteFieldBegin("Content", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Content); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TraceSummaryContent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TraceSummaryContent(%+v)", *p)

}

type MessageItem struct {
	// role
	Role *string `thrift:"Role,1,optional" json:"role" form:"Role" query:"Role"`
	// content
	Content *string `thrift:"Content,2,optional" json:"content" form:"Content" query:"Content"`
}

func NewMessageItem() *MessageItem {
	return &MessageItem{}
}

func (p *MessageItem) InitDefault() {
}

var MessageItem_Role_DEFAULT string

func (p *MessageItem) GetRole() (v string) {
	if !p.IsSetRole() {
		return MessageItem_Role_DEFAULT
	}
	return *p.Role
}

var MessageItem_Content_DEFAULT string

func (p *MessageItem) GetContent() (v string) {
	if !p.IsSetContent() {
		return MessageItem_Content_DEFAULT
	}
	return *p.Content
}

var fieldIDToName_MessageItem = map[int16]string{
	1: "Role",
	2: "Content",
}

func (p *MessageItem) IsSetRole() bool {
	return p.Role != nil
}

func (p *MessageItem) IsSetContent() bool {
	return p.Content != nil
}

func (p *MessageItem) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MessageItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MessageItem) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Role = _field
	return nil
}
func (p *MessageItem) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Content = _field
	return nil
}

func (p *MessageItem) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("MessageItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MessageItem) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetRole() {
		if err = oprot.WriteFieldBegin("Role", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Role); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *MessageItem) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetContent() {
		if err = oprot.WriteFieldBegin("Content", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Content); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *MessageItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MessageItem(%+v)", *p)

}

type SpanSummary struct {
	Tags []*FrontendTag `thrift:"Tags,1,optional" json:"tags" form:"Tags" query:"Tags"`
}

func NewSpanSummary() *SpanSummary {
	return &SpanSummary{}
}

func (p *SpanSummary) InitDefault() {
}

var SpanSummary_Tags_DEFAULT []*FrontendTag

func (p *SpanSummary) GetTags() (v []*FrontendTag) {
	if !p.IsSetTags() {
		return SpanSummary_Tags_DEFAULT
	}
	return p.Tags
}

var fieldIDToName_SpanSummary = map[int16]string{
	1: "Tags",
}

func (p *SpanSummary) IsSetTags() bool {
	return p.Tags != nil
}

func (p *SpanSummary) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SpanSummary[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SpanSummary) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FrontendTag, 0, size)
	values := make([]FrontendTag, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tags = _field
	return nil
}

func (p *SpanSummary) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SpanSummary"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SpanSummary) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTags() {
		if err = oprot.WriteFieldBegin("Tags", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tags)); err != nil {
			return err
		}
		for _, v := range p.Tags {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SpanSummary) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SpanSummary(%+v)", *p)

}

type FrontendTag struct {
	Key string `thrift:"Key,1,required" json:"key" form:"Key,required" query:"Key,required"`
	// Multilingual, if there is no configuration value, use the key
	KeyAlias *string `thrift:"KeyAlias,2,optional" json:"key_alias" form:"KeyAlias" query:"KeyAlias"`
	TagType  TagType `thrift:"TagType,3,required" json:"tag_type" form:"TagType,required" query:"TagType,required"`
	Value    *Value  `thrift:"Value,4,optional" json:"value" form:"Value" query:"Value"`
	// Front-end type for front-end processing
	FrontendTagType *FrontedTagType `thrift:"FrontendTagType,5,optional" json:"frontend_tag_type" form:"FrontendTagType" query:"FrontendTagType"`
	// Can it be copied?
	CanCopy *bool `thrift:"canCopy,6,optional" json:"can_copy" form:"canCopy" query:"canCopy"`
}

func NewFrontendTag() *FrontendTag {
	return &FrontendTag{}
}

func (p *FrontendTag) InitDefault() {
}

func (p *FrontendTag) GetKey() (v string) {
	return p.Key
}

var FrontendTag_KeyAlias_DEFAULT string

func (p *FrontendTag) GetKeyAlias() (v string) {
	if !p.IsSetKeyAlias() {
		return FrontendTag_KeyAlias_DEFAULT
	}
	return *p.KeyAlias
}

func (p *FrontendTag) GetTagType() (v TagType) {
	return p.TagType
}

var FrontendTag_Value_DEFAULT *Value

func (p *FrontendTag) GetValue() (v *Value) {
	if !p.IsSetValue() {
		return FrontendTag_Value_DEFAULT
	}
	return p.Value
}

var FrontendTag_FrontendTagType_DEFAULT FrontedTagType

func (p *FrontendTag) GetFrontendTagType() (v FrontedTagType) {
	if !p.IsSetFrontendTagType() {
		return FrontendTag_FrontendTagType_DEFAULT
	}
	return *p.FrontendTagType
}

var FrontendTag_CanCopy_DEFAULT bool

func (p *FrontendTag) GetCanCopy() (v bool) {
	if !p.IsSetCanCopy() {
		return FrontendTag_CanCopy_DEFAULT
	}
	return *p.CanCopy
}

var fieldIDToName_FrontendTag = map[int16]string{
	1: "Key",
	2: "KeyAlias",
	3: "TagType",
	4: "Value",
	5: "FrontendTagType",
	6: "canCopy",
}

func (p *FrontendTag) IsSetKeyAlias() bool {
	return p.KeyAlias != nil
}

func (p *FrontendTag) IsSetValue() bool {
	return p.Value != nil
}

func (p *FrontendTag) IsSetFrontendTagType() bool {
	return p.FrontendTagType != nil
}

func (p *FrontendTag) IsSetCanCopy() bool {
	return p.CanCopy != nil
}

func (p *FrontendTag) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetKey bool = false
	var issetTagType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetKey = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTagType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetKey {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTagType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FrontendTag[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_FrontendTag[fieldId]))
}

func (p *FrontendTag) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Key = _field
	return nil
}
func (p *FrontendTag) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.KeyAlias = _field
	return nil
}
func (p *FrontendTag) ReadField3(iprot thrift.TProtocol) error {

	var _field TagType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TagType(v)
	}
	p.TagType = _field
	return nil
}
func (p *FrontendTag) ReadField4(iprot thrift.TProtocol) error {
	_field := NewValue()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Value = _field
	return nil
}
func (p *FrontendTag) ReadField5(iprot thrift.TProtocol) error {

	var _field *FrontedTagType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := FrontedTagType(v)
		_field = &tmp
	}
	p.FrontendTagType = _field
	return nil
}
func (p *FrontendTag) ReadField6(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CanCopy = _field
	return nil
}

func (p *FrontendTag) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("FrontendTag"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FrontendTag) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Key", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Key); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FrontendTag) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetKeyAlias() {
		if err = oprot.WriteFieldBegin("KeyAlias", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.KeyAlias); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *FrontendTag) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TagType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TagType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *FrontendTag) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetValue() {
		if err = oprot.WriteFieldBegin("Value", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Value.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *FrontendTag) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetFrontendTagType() {
		if err = oprot.WriteFieldBegin("FrontendTagType", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.FrontendTagType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *FrontendTag) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetCanCopy() {
		if err = oprot.WriteFieldBegin("canCopy", thrift.BOOL, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.CanCopy); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *FrontendTag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FrontendTag(%+v)", *p)

}

type TraceSummary struct {
	// System 1 text
	System *string `thrift:"System,1,optional" json:"system" form:"System" query:"System"`
	// Level 1 chat history
	HistoryMessages []*MessageItem `thrift:"HistoryMessages,2,optional" json:"history_messages" form:"HistoryMessages" query:"HistoryMessages"`
	KeyScenes       []*KeyScene    `thrift:"KeyScenes,3,optional" json:"key_scenes" form:"KeyScenes" query:"KeyScenes"`
	// input
	Input *string `thrift:"Input,4,optional" json:"input" form:"Input" query:"Input"`
	// output
	Output *string `thrift:"Output,5,optional" json:"output" form:"Output" query:"Output"`
	// The duration of the current conversation, in milliseconds
	Duration *int64 `thrift:"Duration,6,optional" json:"duration" form:"Duration" query:"Duration"`
	// user ID
	UserID *string `thrift:"UserID,7,optional" json:"user_id" form:"UserID" query:"UserID"`
}

func NewTraceSummary() *TraceSummary {
	return &TraceSummary{}
}

func (p *TraceSummary) InitDefault() {
}

var TraceSummary_System_DEFAULT string

func (p *TraceSummary) GetSystem() (v string) {
	if !p.IsSetSystem() {
		return TraceSummary_System_DEFAULT
	}
	return *p.System
}

var TraceSummary_HistoryMessages_DEFAULT []*MessageItem

func (p *TraceSummary) GetHistoryMessages() (v []*MessageItem) {
	if !p.IsSetHistoryMessages() {
		return TraceSummary_HistoryMessages_DEFAULT
	}
	return p.HistoryMessages
}

var TraceSummary_KeyScenes_DEFAULT []*KeyScene

func (p *TraceSummary) GetKeyScenes() (v []*KeyScene) {
	if !p.IsSetKeyScenes() {
		return TraceSummary_KeyScenes_DEFAULT
	}
	return p.KeyScenes
}

var TraceSummary_Input_DEFAULT string

func (p *TraceSummary) GetInput() (v string) {
	if !p.IsSetInput() {
		return TraceSummary_Input_DEFAULT
	}
	return *p.Input
}

var TraceSummary_Output_DEFAULT string

func (p *TraceSummary) GetOutput() (v string) {
	if !p.IsSetOutput() {
		return TraceSummary_Output_DEFAULT
	}
	return *p.Output
}

var TraceSummary_Duration_DEFAULT int64

func (p *TraceSummary) GetDuration() (v int64) {
	if !p.IsSetDuration() {
		return TraceSummary_Duration_DEFAULT
	}
	return *p.Duration
}

var TraceSummary_UserID_DEFAULT string

func (p *TraceSummary) GetUserID() (v string) {
	if !p.IsSetUserID() {
		return TraceSummary_UserID_DEFAULT
	}
	return *p.UserID
}

var fieldIDToName_TraceSummary = map[int16]string{
	1: "System",
	2: "HistoryMessages",
	3: "KeyScenes",
	4: "Input",
	5: "Output",
	6: "Duration",
	7: "UserID",
}

func (p *TraceSummary) IsSetSystem() bool {
	return p.System != nil
}

func (p *TraceSummary) IsSetHistoryMessages() bool {
	return p.HistoryMessages != nil
}

func (p *TraceSummary) IsSetKeyScenes() bool {
	return p.KeyScenes != nil
}

func (p *TraceSummary) IsSetInput() bool {
	return p.Input != nil
}

func (p *TraceSummary) IsSetOutput() bool {
	return p.Output != nil
}

func (p *TraceSummary) IsSetDuration() bool {
	return p.Duration != nil
}

func (p *TraceSummary) IsSetUserID() bool {
	return p.UserID != nil
}

func (p *TraceSummary) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TraceSummary[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TraceSummary) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.System = _field
	return nil
}
func (p *TraceSummary) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*MessageItem, 0, size)
	values := make([]MessageItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.HistoryMessages = _field
	return nil
}
func (p *TraceSummary) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*KeyScene, 0, size)
	values := make([]KeyScene, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.KeyScenes = _field
	return nil
}
func (p *TraceSummary) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Input = _field
	return nil
}
func (p *TraceSummary) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Output = _field
	return nil
}
func (p *TraceSummary) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Duration = _field
	return nil
}
func (p *TraceSummary) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UserID = _field
	return nil
}

func (p *TraceSummary) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TraceSummary"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TraceSummary) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSystem() {
		if err = oprot.WriteFieldBegin("System", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.System); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TraceSummary) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetHistoryMessages() {
		if err = oprot.WriteFieldBegin("HistoryMessages", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.HistoryMessages)); err != nil {
			return err
		}
		for _, v := range p.HistoryMessages {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *TraceSummary) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetKeyScenes() {
		if err = oprot.WriteFieldBegin("KeyScenes", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.KeyScenes)); err != nil {
			return err
		}
		for _, v := range p.KeyScenes {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *TraceSummary) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetInput() {
		if err = oprot.WriteFieldBegin("Input", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Input); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *TraceSummary) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetOutput() {
		if err = oprot.WriteFieldBegin("Output", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Output); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *TraceSummary) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDuration() {
		if err = oprot.WriteFieldBegin("Duration", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Duration); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *TraceSummary) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserID() {
		if err = oprot.WriteFieldBegin("UserID", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UserID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *TraceSummary) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TraceSummary(%+v)", *p)

}

type TraceHeader struct {
	// It's in milliseconds.
	Duration *int64 `thrift:"Duration,1,optional" json:"duration" form:"Duration" query:"Duration"`
	// Enter the number of tokens consumed
	Tokens     *int32         `thrift:"Tokens,2,optional" form:"Tokens" json:"Tokens,omitempty" query:"Tokens"`
	StatusCode *int32         `thrift:"StatusCode,3,optional" json:"status_code" form:"StatusCode" query:"StatusCode"`
	Tags       []*FrontendTag `thrift:"Tags,4,optional" json:"tags" form:"Tags" query:"Tags"`
	// Message ID
	MessageID *string `thrift:"MessageID,5,optional" json:"message_id" form:"MessageID" query:"MessageID"`
	// It's in milliseconds.
	StartTime *int64 `thrift:"StartTime,6,optional" json:"start_time" form:"StartTime" query:"StartTime"`
}

func NewTraceHeader() *TraceHeader {
	return &TraceHeader{}
}

func (p *TraceHeader) InitDefault() {
}

var TraceHeader_Duration_DEFAULT int64

func (p *TraceHeader) GetDuration() (v int64) {
	if !p.IsSetDuration() {
		return TraceHeader_Duration_DEFAULT
	}
	return *p.Duration
}

var TraceHeader_Tokens_DEFAULT int32

func (p *TraceHeader) GetTokens() (v int32) {
	if !p.IsSetTokens() {
		return TraceHeader_Tokens_DEFAULT
	}
	return *p.Tokens
}

var TraceHeader_StatusCode_DEFAULT int32

func (p *TraceHeader) GetStatusCode() (v int32) {
	if !p.IsSetStatusCode() {
		return TraceHeader_StatusCode_DEFAULT
	}
	return *p.StatusCode
}

var TraceHeader_Tags_DEFAULT []*FrontendTag

func (p *TraceHeader) GetTags() (v []*FrontendTag) {
	if !p.IsSetTags() {
		return TraceHeader_Tags_DEFAULT
	}
	return p.Tags
}

var TraceHeader_MessageID_DEFAULT string

func (p *TraceHeader) GetMessageID() (v string) {
	if !p.IsSetMessageID() {
		return TraceHeader_MessageID_DEFAULT
	}
	return *p.MessageID
}

var TraceHeader_StartTime_DEFAULT int64

func (p *TraceHeader) GetStartTime() (v int64) {
	if !p.IsSetStartTime() {
		return TraceHeader_StartTime_DEFAULT
	}
	return *p.StartTime
}

var fieldIDToName_TraceHeader = map[int16]string{
	1: "Duration",
	2: "Tokens",
	3: "StatusCode",
	4: "Tags",
	5: "MessageID",
	6: "StartTime",
}

func (p *TraceHeader) IsSetDuration() bool {
	return p.Duration != nil
}

func (p *TraceHeader) IsSetTokens() bool {
	return p.Tokens != nil
}

func (p *TraceHeader) IsSetStatusCode() bool {
	return p.StatusCode != nil
}

func (p *TraceHeader) IsSetTags() bool {
	return p.Tags != nil
}

func (p *TraceHeader) IsSetMessageID() bool {
	return p.MessageID != nil
}

func (p *TraceHeader) IsSetStartTime() bool {
	return p.StartTime != nil
}

func (p *TraceHeader) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TraceHeader[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TraceHeader) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Duration = _field
	return nil
}
func (p *TraceHeader) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Tokens = _field
	return nil
}
func (p *TraceHeader) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StatusCode = _field
	return nil
}
func (p *TraceHeader) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FrontendTag, 0, size)
	values := make([]FrontendTag, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tags = _field
	return nil
}
func (p *TraceHeader) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MessageID = _field
	return nil
}
func (p *TraceHeader) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StartTime = _field
	return nil
}

func (p *TraceHeader) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TraceHeader"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TraceHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetDuration() {
		if err = oprot.WriteFieldBegin("Duration", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Duration); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TraceHeader) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTokens() {
		if err = oprot.WriteFieldBegin("Tokens", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Tokens); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *TraceHeader) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatusCode() {
		if err = oprot.WriteFieldBegin("StatusCode", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.StatusCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *TraceHeader) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTags() {
		if err = oprot.WriteFieldBegin("Tags", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tags)); err != nil {
			return err
		}
		for _, v := range p.Tags {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *TraceHeader) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessageID() {
		if err = oprot.WriteFieldBegin("MessageID", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.MessageID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *TraceHeader) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetStartTime() {
		if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.StartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *TraceHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TraceHeader(%+v)", *p)

}

type TraceFrontend struct {
	Spans  []*TraceFrontendSpan `thrift:"spans,1,optional" json:"spans" form:"spans" `
	Header *TraceHeader         `thrift:"header,2,optional" json:"header" form:"header" query:"header"`
}

func NewTraceFrontend() *TraceFrontend {
	return &TraceFrontend{}
}

func (p *TraceFrontend) InitDefault() {
}

var TraceFrontend_Spans_DEFAULT []*TraceFrontendSpan

func (p *TraceFrontend) GetSpans() (v []*TraceFrontendSpan) {
	if !p.IsSetSpans() {
		return TraceFrontend_Spans_DEFAULT
	}
	return p.Spans
}

var TraceFrontend_Header_DEFAULT *TraceHeader

func (p *TraceFrontend) GetHeader() (v *TraceHeader) {
	if !p.IsSetHeader() {
		return TraceFrontend_Header_DEFAULT
	}
	return p.Header
}

var fieldIDToName_TraceFrontend = map[int16]string{
	1: "spans",
	2: "header",
}

func (p *TraceFrontend) IsSetSpans() bool {
	return p.Spans != nil
}

func (p *TraceFrontend) IsSetHeader() bool {
	return p.Header != nil
}

func (p *TraceFrontend) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TraceFrontend[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TraceFrontend) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TraceFrontendSpan, 0, size)
	values := make([]TraceFrontendSpan, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Spans = _field
	return nil
}
func (p *TraceFrontend) ReadField2(iprot thrift.TProtocol) error {
	_field := NewTraceHeader()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Header = _field
	return nil
}

func (p *TraceFrontend) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TraceFrontend"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TraceFrontend) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSpans() {
		if err = oprot.WriteFieldBegin("spans", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Spans)); err != nil {
			return err
		}
		for _, v := range p.Spans {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TraceFrontend) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetHeader() {
		if err = oprot.WriteFieldBegin("header", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Header.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TraceFrontend) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TraceFrontend(%+v)", *p)

}

type TraceFrontendDoubaoCiciDebug struct {
	Spans   []*TraceFrontendSpan `thrift:"spans,1,optional" json:"spans" form:"spans" `
	Header  *TraceHeader         `thrift:"header,2,optional" json:"header" form:"header" query:"header"`
	Summary *TraceSummary        `thrift:"summary,3,optional" json:"summary" form:"summary" query:"summary"`
}

func NewTraceFrontendDoubaoCiciDebug() *TraceFrontendDoubaoCiciDebug {
	return &TraceFrontendDoubaoCiciDebug{}
}

func (p *TraceFrontendDoubaoCiciDebug) InitDefault() {
}

var TraceFrontendDoubaoCiciDebug_Spans_DEFAULT []*TraceFrontendSpan

func (p *TraceFrontendDoubaoCiciDebug) GetSpans() (v []*TraceFrontendSpan) {
	if !p.IsSetSpans() {
		return TraceFrontendDoubaoCiciDebug_Spans_DEFAULT
	}
	return p.Spans
}

var TraceFrontendDoubaoCiciDebug_Header_DEFAULT *TraceHeader

func (p *TraceFrontendDoubaoCiciDebug) GetHeader() (v *TraceHeader) {
	if !p.IsSetHeader() {
		return TraceFrontendDoubaoCiciDebug_Header_DEFAULT
	}
	return p.Header
}

var TraceFrontendDoubaoCiciDebug_Summary_DEFAULT *TraceSummary

func (p *TraceFrontendDoubaoCiciDebug) GetSummary() (v *TraceSummary) {
	if !p.IsSetSummary() {
		return TraceFrontendDoubaoCiciDebug_Summary_DEFAULT
	}
	return p.Summary
}

var fieldIDToName_TraceFrontendDoubaoCiciDebug = map[int16]string{
	1: "spans",
	2: "header",
	3: "summary",
}

func (p *TraceFrontendDoubaoCiciDebug) IsSetSpans() bool {
	return p.Spans != nil
}

func (p *TraceFrontendDoubaoCiciDebug) IsSetHeader() bool {
	return p.Header != nil
}

func (p *TraceFrontendDoubaoCiciDebug) IsSetSummary() bool {
	return p.Summary != nil
}

func (p *TraceFrontendDoubaoCiciDebug) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TraceFrontendDoubaoCiciDebug[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TraceFrontendDoubaoCiciDebug) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TraceFrontendSpan, 0, size)
	values := make([]TraceFrontendSpan, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Spans = _field
	return nil
}
func (p *TraceFrontendDoubaoCiciDebug) ReadField2(iprot thrift.TProtocol) error {
	_field := NewTraceHeader()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Header = _field
	return nil
}
func (p *TraceFrontendDoubaoCiciDebug) ReadField3(iprot thrift.TProtocol) error {
	_field := NewTraceSummary()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Summary = _field
	return nil
}

func (p *TraceFrontendDoubaoCiciDebug) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TraceFrontendDoubaoCiciDebug"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TraceFrontendDoubaoCiciDebug) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSpans() {
		if err = oprot.WriteFieldBegin("spans", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Spans)); err != nil {
			return err
		}
		for _, v := range p.Spans {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TraceFrontendDoubaoCiciDebug) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetHeader() {
		if err = oprot.WriteFieldBegin("header", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Header.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *TraceFrontendDoubaoCiciDebug) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSummary() {
		if err = oprot.WriteFieldBegin("summary", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Summary.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *TraceFrontendDoubaoCiciDebug) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TraceFrontendDoubaoCiciDebug(%+v)", *p)

}

type SpanInputOutput struct {
	// TEXT
	Type    *InputOutputType `thrift:"Type,1,optional" json:"type" form:"Type" query:"Type"`
	Content *string          `thrift:"Content,2,optional" json:"content" form:"Content" query:"Content"`
}

func NewSpanInputOutput() *SpanInputOutput {
	return &SpanInputOutput{}
}

func (p *SpanInputOutput) InitDefault() {
}

var SpanInputOutput_Type_DEFAULT InputOutputType

func (p *SpanInputOutput) GetType() (v InputOutputType) {
	if !p.IsSetType() {
		return SpanInputOutput_Type_DEFAULT
	}
	return *p.Type
}

var SpanInputOutput_Content_DEFAULT string

func (p *SpanInputOutput) GetContent() (v string) {
	if !p.IsSetContent() {
		return SpanInputOutput_Content_DEFAULT
	}
	return *p.Content
}

var fieldIDToName_SpanInputOutput = map[int16]string{
	1: "Type",
	2: "Content",
}

func (p *SpanInputOutput) IsSetType() bool {
	return p.Type != nil
}

func (p *SpanInputOutput) IsSetContent() bool {
	return p.Content != nil
}

func (p *SpanInputOutput) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SpanInputOutput[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SpanInputOutput) ReadField1(iprot thrift.TProtocol) error {

	var _field *InputOutputType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InputOutputType(v)
		_field = &tmp
	}
	p.Type = _field
	return nil
}
func (p *SpanInputOutput) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Content = _field
	return nil
}

func (p *SpanInputOutput) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SpanInputOutput"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SpanInputOutput) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetType() {
		if err = oprot.WriteFieldBegin("Type", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Type)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SpanInputOutput) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetContent() {
		if err = oprot.WriteFieldBegin("Content", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Content); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SpanInputOutput) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SpanInputOutput(%+v)", *p)

}

type TraceFrontendSpan struct {
	TraceID   string `thrift:"TraceID,1" json:"trace_id" form:"TraceID" query:"TraceID"`
	LogID     string `thrift:"LogID,2" json:"log_id" form:"LogID" query:"LogID"`
	SpanID    string `thrift:"SpanID,3" json:"span_id" form:"SpanID" query:"SpanID"`
	Type      string `thrift:"Type,4" json:"type" form:"Type" query:"Type"`
	Name      string `thrift:"Name,5" json:"name" form:"Name" query:"Name"`
	AliasName string `thrift:"AliasName,6" json:"alias_name" form:"AliasName" query:"AliasName"`
	ParentID  string `thrift:"ParentID,7" json:"parent_id" form:"ParentID" query:"ParentID"`
	// It's in milliseconds.
	Duration int64 `thrift:"Duration,8" json:"duration" form:"Duration" query:"Duration"`
	// It's in milliseconds.
	StartTime  int64       `thrift:"StartTime,9" json:"start_time" form:"StartTime" query:"StartTime"`
	StatusCode int32       `thrift:"StatusCode,10" json:"status_code" form:"StatusCode" query:"StatusCode"`
	Tags       []*TraceTag `thrift:"Tags,11,optional" json:"tags" form:"Tags" query:"Tags"`
	// node details
	Summary *SpanSummary     `thrift:"summary,12,optional" json:"summary" form:"summary" query:"summary"`
	Input   *SpanInputOutput `thrift:"Input,13,optional" json:"input" form:"Input" query:"Input"`
	Output  *SpanInputOutput `thrift:"Output,14,optional" json:"output" form:"Output" query:"Output"`
	// Is it an entry node?
	IsEntry *bool `thrift:"IsEntry,15,optional" json:"is_entry" form:"IsEntry" query:"IsEntry"`
	// product line
	ProductLine *string `thrift:"ProductLine,16,optional" json:"product_line" form:"ProductLine" query:"ProductLine"`
	// Is it a key node?
	IsKeySpan *bool `thrift:"IsKeySpan,17,optional" json:"is_key_span" form:"IsKeySpan" query:"IsKeySpan"`
	// Node owner list, mailbox prefix
	OwnerList []string `thrift:"OwnerList,18,optional" json:"owner_list" form:"OwnerList" query:"OwnerList"`
	// Node Details Document
	RundownDocURL *string `thrift:"RundownDocURL,19,optional" json:"rundown_doc_url" form:"RundownDocURL" query:"RundownDocURL"`
}

func NewTraceFrontendSpan() *TraceFrontendSpan {
	return &TraceFrontendSpan{}
}

func (p *TraceFrontendSpan) InitDefault() {
}

func (p *TraceFrontendSpan) GetTraceID() (v string) {
	return p.TraceID
}

func (p *TraceFrontendSpan) GetLogID() (v string) {
	return p.LogID
}

func (p *TraceFrontendSpan) GetSpanID() (v string) {
	return p.SpanID
}

func (p *TraceFrontendSpan) GetType() (v string) {
	return p.Type
}

func (p *TraceFrontendSpan) GetName() (v string) {
	return p.Name
}

func (p *TraceFrontendSpan) GetAliasName() (v string) {
	return p.AliasName
}

func (p *TraceFrontendSpan) GetParentID() (v string) {
	return p.ParentID
}

func (p *TraceFrontendSpan) GetDuration() (v int64) {
	return p.Duration
}

func (p *TraceFrontendSpan) GetStartTime() (v int64) {
	return p.StartTime
}

func (p *TraceFrontendSpan) GetStatusCode() (v int32) {
	return p.StatusCode
}

var TraceFrontendSpan_Tags_DEFAULT []*TraceTag

func (p *TraceFrontendSpan) GetTags() (v []*TraceTag) {
	if !p.IsSetTags() {
		return TraceFrontendSpan_Tags_DEFAULT
	}
	return p.Tags
}

var TraceFrontendSpan_Summary_DEFAULT *SpanSummary

func (p *TraceFrontendSpan) GetSummary() (v *SpanSummary) {
	if !p.IsSetSummary() {
		return TraceFrontendSpan_Summary_DEFAULT
	}
	return p.Summary
}

var TraceFrontendSpan_Input_DEFAULT *SpanInputOutput

func (p *TraceFrontendSpan) GetInput() (v *SpanInputOutput) {
	if !p.IsSetInput() {
		return TraceFrontendSpan_Input_DEFAULT
	}
	return p.Input
}

var TraceFrontendSpan_Output_DEFAULT *SpanInputOutput

func (p *TraceFrontendSpan) GetOutput() (v *SpanInputOutput) {
	if !p.IsSetOutput() {
		return TraceFrontendSpan_Output_DEFAULT
	}
	return p.Output
}

var TraceFrontendSpan_IsEntry_DEFAULT bool

func (p *TraceFrontendSpan) GetIsEntry() (v bool) {
	if !p.IsSetIsEntry() {
		return TraceFrontendSpan_IsEntry_DEFAULT
	}
	return *p.IsEntry
}

var TraceFrontendSpan_ProductLine_DEFAULT string

func (p *TraceFrontendSpan) GetProductLine() (v string) {
	if !p.IsSetProductLine() {
		return TraceFrontendSpan_ProductLine_DEFAULT
	}
	return *p.ProductLine
}

var TraceFrontendSpan_IsKeySpan_DEFAULT bool

func (p *TraceFrontendSpan) GetIsKeySpan() (v bool) {
	if !p.IsSetIsKeySpan() {
		return TraceFrontendSpan_IsKeySpan_DEFAULT
	}
	return *p.IsKeySpan
}

var TraceFrontendSpan_OwnerList_DEFAULT []string

func (p *TraceFrontendSpan) GetOwnerList() (v []string) {
	if !p.IsSetOwnerList() {
		return TraceFrontendSpan_OwnerList_DEFAULT
	}
	return p.OwnerList
}

var TraceFrontendSpan_RundownDocURL_DEFAULT string

func (p *TraceFrontendSpan) GetRundownDocURL() (v string) {
	if !p.IsSetRundownDocURL() {
		return TraceFrontendSpan_RundownDocURL_DEFAULT
	}
	return *p.RundownDocURL
}

var fieldIDToName_TraceFrontendSpan = map[int16]string{
	1:  "TraceID",
	2:  "LogID",
	3:  "SpanID",
	4:  "Type",
	5:  "Name",
	6:  "AliasName",
	7:  "ParentID",
	8:  "Duration",
	9:  "StartTime",
	10: "StatusCode",
	11: "Tags",
	12: "summary",
	13: "Input",
	14: "Output",
	15: "IsEntry",
	16: "ProductLine",
	17: "IsKeySpan",
	18: "OwnerList",
	19: "RundownDocURL",
}

func (p *TraceFrontendSpan) IsSetTags() bool {
	return p.Tags != nil
}

func (p *TraceFrontendSpan) IsSetSummary() bool {
	return p.Summary != nil
}

func (p *TraceFrontendSpan) IsSetInput() bool {
	return p.Input != nil
}

func (p *TraceFrontendSpan) IsSetOutput() bool {
	return p.Output != nil
}

func (p *TraceFrontendSpan) IsSetIsEntry() bool {
	return p.IsEntry != nil
}

func (p *TraceFrontendSpan) IsSetProductLine() bool {
	return p.ProductLine != nil
}

func (p *TraceFrontendSpan) IsSetIsKeySpan() bool {
	return p.IsKeySpan != nil
}

func (p *TraceFrontendSpan) IsSetOwnerList() bool {
	return p.OwnerList != nil
}

func (p *TraceFrontendSpan) IsSetRundownDocURL() bool {
	return p.RundownDocURL != nil
}

func (p *TraceFrontendSpan) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TraceFrontendSpan[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TraceFrontendSpan) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TraceID = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LogID = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SpanID = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AliasName = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ParentID = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField8(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Duration = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField9(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField10(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StatusCode = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TraceTag, 0, size)
	values := make([]TraceTag, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tags = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField12(iprot thrift.TProtocol) error {
	_field := NewSpanSummary()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Summary = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField13(iprot thrift.TProtocol) error {
	_field := NewSpanInputOutput()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Input = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField14(iprot thrift.TProtocol) error {
	_field := NewSpanInputOutput()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Output = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField15(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsEntry = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField16(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProductLine = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField17(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsKeySpan = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField18(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.OwnerList = _field
	return nil
}
func (p *TraceFrontendSpan) ReadField19(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RundownDocURL = _field
	return nil
}

func (p *TraceFrontendSpan) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TraceFrontendSpan"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TraceFrontendSpan) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TraceID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TraceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LogID", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LogID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SpanID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SpanID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Type", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AliasName", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AliasName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ParentID", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ParentID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Duration", thrift.I64, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Duration); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I64, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StatusCode", thrift.I32, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StatusCode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetTags() {
		if err = oprot.WriteFieldBegin("Tags", thrift.LIST, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tags)); err != nil {
			return err
		}
		for _, v := range p.Tags {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetSummary() {
		if err = oprot.WriteFieldBegin("summary", thrift.STRUCT, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Summary.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetInput() {
		if err = oprot.WriteFieldBegin("Input", thrift.STRUCT, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Input.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetOutput() {
		if err = oprot.WriteFieldBegin("Output", thrift.STRUCT, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Output.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsEntry() {
		if err = oprot.WriteFieldBegin("IsEntry", thrift.BOOL, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsEntry); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetProductLine() {
		if err = oprot.WriteFieldBegin("ProductLine", thrift.STRING, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProductLine); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsKeySpan() {
		if err = oprot.WriteFieldBegin("IsKeySpan", thrift.BOOL, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsKeySpan); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetOwnerList() {
		if err = oprot.WriteFieldBegin("OwnerList", thrift.LIST, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.OwnerList)); err != nil {
			return err
		}
		for _, v := range p.OwnerList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}
func (p *TraceFrontendSpan) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetRundownDocURL() {
		if err = oprot.WriteFieldBegin("RundownDocURL", thrift.STRING, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RundownDocURL); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *TraceFrontendSpan) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TraceFrontendSpan(%+v)", *p)

}
