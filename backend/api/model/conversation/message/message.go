// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package message

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
	"github.com/coze-dev/coze-studio/backend/api/model/base"
	"github.com/coze-dev/coze-studio/backend/api/model/conversation/common"
)

const (
	OrderByDesc = "DESC"

	OrderByAsc = "ASC"
)

type LoadDirection int64

const (
	LoadDirection_Unknown LoadDirection = 0
	LoadDirection_Prev    LoadDirection = 1
	LoadDirection_Next    LoadDirection = 2
)

func (p LoadDirection) String() string {
	switch p {
	case LoadDirection_Unknown:
		return "Unknown"
	case LoadDirection_Prev:
		return "Prev"
	case LoadDirection_Next:
		return "Next"
	}
	return "<UNSET>"
}

func LoadDirectionFromString(s string) (LoadDirection, error) {
	switch s {
	case "Unknown":
		return LoadDirection_Unknown, nil
	case "Prev":
		return LoadDirection_Prev, nil
	case "Next":
		return LoadDirection_Next, nil
	}
	return LoadDirection(0), fmt.Errorf("not a valid LoadDirection string")
}

func LoadDirectionPtr(v LoadDirection) *LoadDirection { return &v }
func (p *LoadDirection) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = LoadDirection(result.Int64)
	return
}

func (p *LoadDirection) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type MsgParticipantType int64

const (
	MsgParticipantType_Bot  MsgParticipantType = 1
	MsgParticipantType_User MsgParticipantType = 2
)

func (p MsgParticipantType) String() string {
	switch p {
	case MsgParticipantType_Bot:
		return "Bot"
	case MsgParticipantType_User:
		return "User"
	}
	return "<UNSET>"
}

func MsgParticipantTypeFromString(s string) (MsgParticipantType, error) {
	switch s {
	case "Bot":
		return MsgParticipantType_Bot, nil
	case "User":
		return MsgParticipantType_User, nil
	}
	return MsgParticipantType(0), fmt.Errorf("not a valid MsgParticipantType string")
}

func MsgParticipantTypePtr(v MsgParticipantType) *MsgParticipantType { return &v }
func (p *MsgParticipantType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = MsgParticipantType(result.Int64)
	return
}

func (p *MsgParticipantType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// Enumeration following copilot definition
type ChatMessageMetaType int64

const (
	// Compatible value
	ChatMessageMetaType_Default_0 ChatMessageMetaType = 0
	// End-to-side direct replacement
	ChatMessageMetaType_Replaceable ChatMessageMetaType = 1
	// insert reference
	ChatMessageMetaType_Insertable ChatMessageMetaType = 2
	// document citation
	ChatMessageMetaType_DocumentRef ChatMessageMetaType = 3
	// Knowledge Base Reference Card
	ChatMessageMetaType_KnowledgeCard ChatMessageMetaType = 4
	// The embedded multimedia information is only used by Alice for the end. Because full link multiplexing uses this field, it has been changed here.
	ChatMessageMetaType_EmbeddedMultimedia ChatMessageMetaType = 100
)

func (p ChatMessageMetaType) String() string {
	switch p {
	case ChatMessageMetaType_Default_0:
		return "Default_0"
	case ChatMessageMetaType_Replaceable:
		return "Replaceable"
	case ChatMessageMetaType_Insertable:
		return "Insertable"
	case ChatMessageMetaType_DocumentRef:
		return "DocumentRef"
	case ChatMessageMetaType_KnowledgeCard:
		return "KnowledgeCard"
	case ChatMessageMetaType_EmbeddedMultimedia:
		return "EmbeddedMultimedia"
	}
	return "<UNSET>"
}

func ChatMessageMetaTypeFromString(s string) (ChatMessageMetaType, error) {
	switch s {
	case "Default_0":
		return ChatMessageMetaType_Default_0, nil
	case "Replaceable":
		return ChatMessageMetaType_Replaceable, nil
	case "Insertable":
		return ChatMessageMetaType_Insertable, nil
	case "DocumentRef":
		return ChatMessageMetaType_DocumentRef, nil
	case "KnowledgeCard":
		return ChatMessageMetaType_KnowledgeCard, nil
	case "EmbeddedMultimedia":
		return ChatMessageMetaType_EmbeddedMultimedia, nil
	}
	return ChatMessageMetaType(0), fmt.Errorf("not a valid ChatMessageMetaType string")
}

func ChatMessageMetaTypePtr(v ChatMessageMetaType) *ChatMessageMetaType { return &v }
func (p *ChatMessageMetaType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ChatMessageMetaType(result.Int64)
	return
}

func (p *ChatMessageMetaType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ExtraInfo struct {
	LocalMessageID string `thrift:"local_message_id,1" form:"local_message_id" json:"local_message_id" query:"local_message_id"`
	InputTokens    string `thrift:"input_tokens,2" form:"input_tokens" json:"input_tokens" query:"input_tokens"`
	OutputTokens   string `thrift:"output_tokens,3" form:"output_tokens" json:"output_tokens" query:"output_tokens"`
	Token          string `thrift:"token,4" form:"token" json:"token" query:"token"`
	// "success" or "fail"
	PluginStatus        string `thrift:"plugin_status,5" form:"plugin_status" json:"plugin_status" query:"plugin_status"`
	TimeCost            string `thrift:"time_cost,6" form:"time_cost" json:"time_cost" query:"time_cost"`
	WorkflowTokens      string `thrift:"workflow_tokens,7" form:"workflow_tokens" json:"workflow_tokens" query:"workflow_tokens"`
	BotState            string `thrift:"bot_state,8" form:"bot_state" json:"bot_state" query:"bot_state"`
	PluginRequest       string `thrift:"plugin_request,9" form:"plugin_request" json:"plugin_request" query:"plugin_request"`
	ToolName            string `thrift:"tool_name,10" form:"tool_name" json:"tool_name" query:"tool_name"`
	Plugin              string `thrift:"plugin,11" form:"plugin" json:"plugin" query:"plugin"`
	MockHitInfo         string `thrift:"mock_hit_info,12" form:"mock_hit_info" json:"mock_hit_info" query:"mock_hit_info"`
	LogID               string `thrift:"log_id,13" form:"log_id" json:"log_id" query:"log_id"`
	StreamID            string `thrift:"stream_id,14" form:"stream_id" json:"stream_id" query:"stream_id"`
	MessageTitle        string `thrift:"message_title,15" form:"message_title" json:"message_title" query:"message_title"`
	StreamPluginRunning string `thrift:"stream_plugin_running,16" form:"stream_plugin_running" json:"stream_plugin_running" query:"stream_plugin_running"`
	NewSectionID        string `thrift:"new_section_id,17" form:"new_section_id" json:"new_section_id" query:"new_section_id"`
	RemoveQueryID       string `thrift:"remove_query_id,18" form:"remove_query_id" json:"remove_query_id" query:"remove_query_id"`
	ExecuteDisplayName  string `thrift:"execute_display_name,19" form:"execute_display_name" json:"execute_display_name" query:"execute_display_name"`
	// Corresponding to timed task task_type, 1-preset task, 2-user task, 3-Plugin background task
	TaskType string `thrift:"task_type,20" form:"task_type" json:"task_type" query:"task_type"`
	//Agent app uses reference format
	ReferFormat string `thrift:"refer_format,21" form:"refer_format" json:"refer_format" query:"refer_format"`
	CallID      string `thrift:"call_id,22" form:"call_id" json:"call_id" query:"call_id"`
}

func NewExtraInfo() *ExtraInfo {
	return &ExtraInfo{}
}

func (p *ExtraInfo) InitDefault() {
}

func (p *ExtraInfo) GetLocalMessageID() (v string) {
	return p.LocalMessageID
}

func (p *ExtraInfo) GetInputTokens() (v string) {
	return p.InputTokens
}

func (p *ExtraInfo) GetOutputTokens() (v string) {
	return p.OutputTokens
}

func (p *ExtraInfo) GetToken() (v string) {
	return p.Token
}

func (p *ExtraInfo) GetPluginStatus() (v string) {
	return p.PluginStatus
}

func (p *ExtraInfo) GetTimeCost() (v string) {
	return p.TimeCost
}

func (p *ExtraInfo) GetWorkflowTokens() (v string) {
	return p.WorkflowTokens
}

func (p *ExtraInfo) GetBotState() (v string) {
	return p.BotState
}

func (p *ExtraInfo) GetPluginRequest() (v string) {
	return p.PluginRequest
}

func (p *ExtraInfo) GetToolName() (v string) {
	return p.ToolName
}

func (p *ExtraInfo) GetPlugin() (v string) {
	return p.Plugin
}

func (p *ExtraInfo) GetMockHitInfo() (v string) {
	return p.MockHitInfo
}

func (p *ExtraInfo) GetLogID() (v string) {
	return p.LogID
}

func (p *ExtraInfo) GetStreamID() (v string) {
	return p.StreamID
}

func (p *ExtraInfo) GetMessageTitle() (v string) {
	return p.MessageTitle
}

func (p *ExtraInfo) GetStreamPluginRunning() (v string) {
	return p.StreamPluginRunning
}

func (p *ExtraInfo) GetNewSectionID() (v string) {
	return p.NewSectionID
}

func (p *ExtraInfo) GetRemoveQueryID() (v string) {
	return p.RemoveQueryID
}

func (p *ExtraInfo) GetExecuteDisplayName() (v string) {
	return p.ExecuteDisplayName
}

func (p *ExtraInfo) GetTaskType() (v string) {
	return p.TaskType
}

func (p *ExtraInfo) GetReferFormat() (v string) {
	return p.ReferFormat
}

func (p *ExtraInfo) GetCallID() (v string) {
	return p.CallID
}

var fieldIDToName_ExtraInfo = map[int16]string{
	1:  "local_message_id",
	2:  "input_tokens",
	3:  "output_tokens",
	4:  "token",
	5:  "plugin_status",
	6:  "time_cost",
	7:  "workflow_tokens",
	8:  "bot_state",
	9:  "plugin_request",
	10: "tool_name",
	11: "plugin",
	12: "mock_hit_info",
	13: "log_id",
	14: "stream_id",
	15: "message_title",
	16: "stream_plugin_running",
	17: "new_section_id",
	18: "remove_query_id",
	19: "execute_display_name",
	20: "task_type",
	21: "refer_format",
	22: "call_id",
}

func (p *ExtraInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExtraInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ExtraInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LocalMessageID = _field
	return nil
}
func (p *ExtraInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InputTokens = _field
	return nil
}
func (p *ExtraInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OutputTokens = _field
	return nil
}
func (p *ExtraInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Token = _field
	return nil
}
func (p *ExtraInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PluginStatus = _field
	return nil
}
func (p *ExtraInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TimeCost = _field
	return nil
}
func (p *ExtraInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkflowTokens = _field
	return nil
}
func (p *ExtraInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BotState = _field
	return nil
}
func (p *ExtraInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PluginRequest = _field
	return nil
}
func (p *ExtraInfo) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ToolName = _field
	return nil
}
func (p *ExtraInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Plugin = _field
	return nil
}
func (p *ExtraInfo) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MockHitInfo = _field
	return nil
}
func (p *ExtraInfo) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LogID = _field
	return nil
}
func (p *ExtraInfo) ReadField14(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StreamID = _field
	return nil
}
func (p *ExtraInfo) ReadField15(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageTitle = _field
	return nil
}
func (p *ExtraInfo) ReadField16(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StreamPluginRunning = _field
	return nil
}
func (p *ExtraInfo) ReadField17(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NewSectionID = _field
	return nil
}
func (p *ExtraInfo) ReadField18(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RemoveQueryID = _field
	return nil
}
func (p *ExtraInfo) ReadField19(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecuteDisplayName = _field
	return nil
}
func (p *ExtraInfo) ReadField20(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskType = _field
	return nil
}
func (p *ExtraInfo) ReadField21(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReferFormat = _field
	return nil
}
func (p *ExtraInfo) ReadField22(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CallID = _field
	return nil
}

func (p *ExtraInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ExtraInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExtraInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("local_message_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LocalMessageID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ExtraInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("input_tokens", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InputTokens); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ExtraInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("output_tokens", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OutputTokens); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ExtraInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("token", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Token); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ExtraInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_status", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PluginStatus); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ExtraInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("time_cost", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TimeCost); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ExtraInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("workflow_tokens", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WorkflowTokens); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *ExtraInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot_state", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BotState); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *ExtraInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_request", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PluginRequest); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *ExtraInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tool_name", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ToolName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *ExtraInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Plugin); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *ExtraInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("mock_hit_info", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MockHitInfo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *ExtraInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("log_id", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LogID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *ExtraInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("stream_id", thrift.STRING, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StreamID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *ExtraInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("message_title", thrift.STRING, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MessageTitle); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *ExtraInfo) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("stream_plugin_running", thrift.STRING, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StreamPluginRunning); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *ExtraInfo) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("new_section_id", thrift.STRING, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NewSectionID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}
func (p *ExtraInfo) writeField18(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("remove_query_id", thrift.STRING, 18); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RemoveQueryID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}
func (p *ExtraInfo) writeField19(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("execute_display_name", thrift.STRING, 19); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecuteDisplayName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}
func (p *ExtraInfo) writeField20(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("task_type", thrift.STRING, 20); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}
func (p *ExtraInfo) writeField21(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refer_format", thrift.STRING, 21); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ReferFormat); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}
func (p *ExtraInfo) writeField22(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("call_id", thrift.STRING, 22); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CallID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}

func (p *ExtraInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExtraInfo(%+v)", *p)

}

type MsgParticipantInfo struct {
	ID           string             `thrift:"id,1" form:"id" json:"id" query:"id"`
	Type         MsgParticipantType `thrift:"type,2" form:"type" json:"type" query:"type"`
	Name         string             `thrift:"name,3" form:"name" json:"name" query:"name"`
	Desc         string             `thrift:"desc,4" form:"desc" json:"desc" query:"desc"`
	AvatarURL    string             `thrift:"avatar_url,5" form:"avatar_url" json:"avatar_url" query:"avatar_url"`
	SpaceID      string             `thrift:"space_id,6" form:"space_id" json:"space_id" query:"space_id"`
	UserID       string             `thrift:"user_id,7" form:"user_id" json:"user_id" query:"user_id"`
	UserName     string             `thrift:"user_name,8" form:"user_name" json:"user_name" query:"user_name"`
	AllowMention bool               `thrift:"allow_mention,9" form:"allow_mention" json:"allow_mention" query:"allow_mention"`
	AccessPath   string             `thrift:"access_path,10" form:"access_path" json:"access_path" query:"access_path"`
	// Is collected
	IsFav bool `thrift:"is_fav,11" form:"is_fav" json:"is_fav" query:"is_fav"`
	//    12: shortcut_command ShortcutStruct shortcuts//Shortcuts
	AllowShare bool `thrift:"allow_share,13" form:"allow_share" json:"allow_share" query:"allow_share"`
}

func NewMsgParticipantInfo() *MsgParticipantInfo {
	return &MsgParticipantInfo{}
}

func (p *MsgParticipantInfo) InitDefault() {
}

func (p *MsgParticipantInfo) GetID() (v string) {
	return p.ID
}

func (p *MsgParticipantInfo) GetType() (v MsgParticipantType) {
	return p.Type
}

func (p *MsgParticipantInfo) GetName() (v string) {
	return p.Name
}

func (p *MsgParticipantInfo) GetDesc() (v string) {
	return p.Desc
}

func (p *MsgParticipantInfo) GetAvatarURL() (v string) {
	return p.AvatarURL
}

func (p *MsgParticipantInfo) GetSpaceID() (v string) {
	return p.SpaceID
}

func (p *MsgParticipantInfo) GetUserID() (v string) {
	return p.UserID
}

func (p *MsgParticipantInfo) GetUserName() (v string) {
	return p.UserName
}

func (p *MsgParticipantInfo) GetAllowMention() (v bool) {
	return p.AllowMention
}

func (p *MsgParticipantInfo) GetAccessPath() (v string) {
	return p.AccessPath
}

func (p *MsgParticipantInfo) GetIsFav() (v bool) {
	return p.IsFav
}

func (p *MsgParticipantInfo) GetAllowShare() (v bool) {
	return p.AllowShare
}

var fieldIDToName_MsgParticipantInfo = map[int16]string{
	1:  "id",
	2:  "type",
	3:  "name",
	4:  "desc",
	5:  "avatar_url",
	6:  "space_id",
	7:  "user_id",
	8:  "user_name",
	9:  "allow_mention",
	10: "access_path",
	11: "is_fav",
	13: "allow_share",
}

func (p *MsgParticipantInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MsgParticipantInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MsgParticipantInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *MsgParticipantInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field MsgParticipantType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = MsgParticipantType(v)
	}
	p.Type = _field
	return nil
}
func (p *MsgParticipantInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *MsgParticipantInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Desc = _field
	return nil
}
func (p *MsgParticipantInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AvatarURL = _field
	return nil
}
func (p *MsgParticipantInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SpaceID = _field
	return nil
}
func (p *MsgParticipantInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *MsgParticipantInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserName = _field
	return nil
}
func (p *MsgParticipantInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowMention = _field
	return nil
}
func (p *MsgParticipantInfo) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccessPath = _field
	return nil
}
func (p *MsgParticipantInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsFav = _field
	return nil
}
func (p *MsgParticipantInfo) ReadField13(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllowShare = _field
	return nil
}

func (p *MsgParticipantInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("MsgParticipantInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MsgParticipantInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *MsgParticipantInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Type)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *MsgParticipantInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *MsgParticipantInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("desc", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Desc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *MsgParticipantInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("avatar_url", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AvatarURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *MsgParticipantInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("space_id", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SpaceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *MsgParticipantInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_id", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *MsgParticipantInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_name", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *MsgParticipantInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("allow_mention", thrift.BOOL, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.AllowMention); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *MsgParticipantInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("access_path", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccessPath); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *MsgParticipantInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("is_fav", thrift.BOOL, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsFav); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *MsgParticipantInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("allow_share", thrift.BOOL, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.AllowShare); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *MsgParticipantInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MsgParticipantInfo(%+v)", *p)

}

//	struct InterruptFunction {
//	 1: string name
//	 2: string arguments
//	}
//
//	struct InterruptRequireInfo {
//	 1: string require_fields
//	 2: string name
//	}
type InterruptPlugin struct {
	ID string `thrift:"id,1" form:"id" json:"id" query:"id"`
	// 1 function, 2 require_info
	Type string `thrift:"type,2" form:"type" json:"type" query:"type"`
}

func NewInterruptPlugin() *InterruptPlugin {
	return &InterruptPlugin{}
}

func (p *InterruptPlugin) InitDefault() {
}

func (p *InterruptPlugin) GetID() (v string) {
	return p.ID
}

func (p *InterruptPlugin) GetType() (v string) {
	return p.Type
}

var fieldIDToName_InterruptPlugin = map[int16]string{
	1: "id",
	2: "type",
}

func (p *InterruptPlugin) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InterruptPlugin[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InterruptPlugin) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *InterruptPlugin) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}

func (p *InterruptPlugin) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("InterruptPlugin"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InterruptPlugin) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *InterruptPlugin) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InterruptPlugin) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InterruptPlugin(%+v)", *p)

}

type SubmitToolOutputs struct {
	ToolCalls []*InterruptPlugin `thrift:"tool_calls,1" form:"tool_calls" json:"tool_calls" query:"tool_calls"`
}

func NewSubmitToolOutputs() *SubmitToolOutputs {
	return &SubmitToolOutputs{}
}

func (p *SubmitToolOutputs) InitDefault() {
}

func (p *SubmitToolOutputs) GetToolCalls() (v []*InterruptPlugin) {
	return p.ToolCalls
}

var fieldIDToName_SubmitToolOutputs = map[int16]string{
	1: "tool_calls",
}

func (p *SubmitToolOutputs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SubmitToolOutputs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SubmitToolOutputs) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InterruptPlugin, 0, size)
	values := make([]InterruptPlugin, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ToolCalls = _field
	return nil
}

func (p *SubmitToolOutputs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SubmitToolOutputs"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SubmitToolOutputs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tool_calls", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ToolCalls)); err != nil {
		return err
	}
	for _, v := range p.ToolCalls {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SubmitToolOutputs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitToolOutputs(%+v)", *p)

}

// Keep up with bot_connector_platform
type RequiredAction struct {
	Type              string             `thrift:"type,1" form:"type" json:"type" query:"type"`
	SubmitToolOutputs *SubmitToolOutputs `thrift:"submit_tool_outputs,2" form:"submit_tool_outputs" json:"submit_tool_outputs" query:"submit_tool_outputs"`
}

func NewRequiredAction() *RequiredAction {
	return &RequiredAction{}
}

func (p *RequiredAction) InitDefault() {
}

func (p *RequiredAction) GetType() (v string) {
	return p.Type
}

var RequiredAction_SubmitToolOutputs_DEFAULT *SubmitToolOutputs

func (p *RequiredAction) GetSubmitToolOutputs() (v *SubmitToolOutputs) {
	if !p.IsSetSubmitToolOutputs() {
		return RequiredAction_SubmitToolOutputs_DEFAULT
	}
	return p.SubmitToolOutputs
}

var fieldIDToName_RequiredAction = map[int16]string{
	1: "type",
	2: "submit_tool_outputs",
}

func (p *RequiredAction) IsSetSubmitToolOutputs() bool {
	return p.SubmitToolOutputs != nil
}

func (p *RequiredAction) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RequiredAction[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RequiredAction) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}
func (p *RequiredAction) ReadField2(iprot thrift.TProtocol) error {
	_field := NewSubmitToolOutputs()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SubmitToolOutputs = _field
	return nil
}

func (p *RequiredAction) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RequiredAction"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RequiredAction) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RequiredAction) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("submit_tool_outputs", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.SubmitToolOutputs.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RequiredAction) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RequiredAction(%+v)", *p)

}

type ChatMessageMetaInfo struct {
	Type ChatMessageMetaType `thrift:"type,1" form:"type" json:"type" query:"type"`
	Info string              `thrift:"info,2" form:"info" json:"info" query:"info"`
}

func NewChatMessageMetaInfo() *ChatMessageMetaInfo {
	return &ChatMessageMetaInfo{}
}

func (p *ChatMessageMetaInfo) InitDefault() {
}

func (p *ChatMessageMetaInfo) GetType() (v ChatMessageMetaType) {
	return p.Type
}

func (p *ChatMessageMetaInfo) GetInfo() (v string) {
	return p.Info
}

var fieldIDToName_ChatMessageMetaInfo = map[int16]string{
	1: "type",
	2: "info",
}

func (p *ChatMessageMetaInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChatMessageMetaInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ChatMessageMetaInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field ChatMessageMetaType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ChatMessageMetaType(v)
	}
	p.Type = _field
	return nil
}
func (p *ChatMessageMetaInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Info = _field
	return nil
}

func (p *ChatMessageMetaInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ChatMessageMetaInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChatMessageMetaInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Type)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ChatMessageMetaInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("info", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Info); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ChatMessageMetaInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChatMessageMetaInfo(%+v)", *p)

}

type ChatMessage struct {
	Role        string     `thrift:"role,1" form:"role" json:"role" query:"role"`
	Type        string     `thrift:"type,2" form:"type" json:"type" query:"type"`
	Content     string     `thrift:"content,3" form:"content" json:"content" query:"content"`
	ContentType string     `thrift:"content_type,4" form:"content_type" json:"content_type" query:"content_type"`
	MessageID   string     `thrift:"message_id,5" form:"message_id" json:"message_id" query:"message_id"`
	ReplyID     string     `thrift:"reply_id,6" form:"reply_id" json:"reply_id" query:"reply_id"`
	SectionID   string     `thrift:"section_id,7" form:"section_id" json:"section_id" query:"section_id"`
	ExtraInfo   *ExtraInfo `thrift:"extra_info,8" form:"extra_info" json:"extra_info" query:"extra_info"`
	// Normal, interrupted state, used when pulling the message list, this field is not available when chat is running.
	Status string `thrift:"status,9" form:"status" json:"status" query:"status"`
	// interrupt position
	BrokenPos    *int32                `thrift:"broken_pos,10,optional" form:"broken_pos" json:"broken_pos,omitempty" query:"broken_pos"`
	SenderID     *string               `thrift:"sender_id,11,optional" form:"sender_id" json:"sender_id,omitempty" query:"sender_id"`
	MentionList  []*MsgParticipantInfo `thrift:"mention_list,12,optional" form:"mention_list" json:"mention_list,omitempty" query:"mention_list"`
	ContentTime  int64                 `thrift:"content_time,13" form:"content_time" json:"content_time" query:"content_time"`
	MessageIndex int64                 `thrift:"message_index,14" json:"message_index,string" form:"message_index" query:"message_index"`
	// Sources, 0 normal chat messages, 1 scheduled task, 2 notifications, 3 asynchronous results
	Source int32 `thrift:"source,15" form:"source" json:"source" query:"source"`
	// Corresponding to the replied query, the backend cannot be found, and a backend is added.
	ReplyMessage *ChatMessage `thrift:"reply_message,16,optional" form:"reply_message" json:"reply_message,omitempty" query:"reply_message"`
	// interrupt message
	RequiredAction *RequiredAction `thrift:"required_action,17,optional" form:"required_action" json:"required_action,omitempty" query:"required_action"`
	// Text markup such as quoting, highlighting, etc
	MetaInfos []*ChatMessageMetaInfo `thrift:"meta_infos,18,optional" form:"meta_infos" json:"meta_infos,omitempty" query:"meta_infos"`
	// Card Status
	CardStatus map[string]string `thrift:"card_status,19,optional" form:"card_status" json:"card_status,omitempty" query:"card_status"`
	//Model Thinking Chain
	ReasoningContent *string `thrift:"reasoning_content,20,optional" form:"reasoning_content" json:"reasoning_content,omitempty" query:"reasoning_content"`
}

func NewChatMessage() *ChatMessage {
	return &ChatMessage{}
}

func (p *ChatMessage) InitDefault() {
}

func (p *ChatMessage) GetRole() (v string) {
	return p.Role
}

func (p *ChatMessage) GetType() (v string) {
	return p.Type
}

func (p *ChatMessage) GetContent() (v string) {
	return p.Content
}

func (p *ChatMessage) GetContentType() (v string) {
	return p.ContentType
}

func (p *ChatMessage) GetMessageID() (v string) {
	return p.MessageID
}

func (p *ChatMessage) GetReplyID() (v string) {
	return p.ReplyID
}

func (p *ChatMessage) GetSectionID() (v string) {
	return p.SectionID
}

var ChatMessage_ExtraInfo_DEFAULT *ExtraInfo

func (p *ChatMessage) GetExtraInfo() (v *ExtraInfo) {
	if !p.IsSetExtraInfo() {
		return ChatMessage_ExtraInfo_DEFAULT
	}
	return p.ExtraInfo
}

func (p *ChatMessage) GetStatus() (v string) {
	return p.Status
}

var ChatMessage_BrokenPos_DEFAULT int32

func (p *ChatMessage) GetBrokenPos() (v int32) {
	if !p.IsSetBrokenPos() {
		return ChatMessage_BrokenPos_DEFAULT
	}
	return *p.BrokenPos
}

var ChatMessage_SenderID_DEFAULT string

func (p *ChatMessage) GetSenderID() (v string) {
	if !p.IsSetSenderID() {
		return ChatMessage_SenderID_DEFAULT
	}
	return *p.SenderID
}

var ChatMessage_MentionList_DEFAULT []*MsgParticipantInfo

func (p *ChatMessage) GetMentionList() (v []*MsgParticipantInfo) {
	if !p.IsSetMentionList() {
		return ChatMessage_MentionList_DEFAULT
	}
	return p.MentionList
}

func (p *ChatMessage) GetContentTime() (v int64) {
	return p.ContentTime
}

func (p *ChatMessage) GetMessageIndex() (v int64) {
	return p.MessageIndex
}

func (p *ChatMessage) GetSource() (v int32) {
	return p.Source
}

var ChatMessage_ReplyMessage_DEFAULT *ChatMessage

func (p *ChatMessage) GetReplyMessage() (v *ChatMessage) {
	if !p.IsSetReplyMessage() {
		return ChatMessage_ReplyMessage_DEFAULT
	}
	return p.ReplyMessage
}

var ChatMessage_RequiredAction_DEFAULT *RequiredAction

func (p *ChatMessage) GetRequiredAction() (v *RequiredAction) {
	if !p.IsSetRequiredAction() {
		return ChatMessage_RequiredAction_DEFAULT
	}
	return p.RequiredAction
}

var ChatMessage_MetaInfos_DEFAULT []*ChatMessageMetaInfo

func (p *ChatMessage) GetMetaInfos() (v []*ChatMessageMetaInfo) {
	if !p.IsSetMetaInfos() {
		return ChatMessage_MetaInfos_DEFAULT
	}
	return p.MetaInfos
}

var ChatMessage_CardStatus_DEFAULT map[string]string

func (p *ChatMessage) GetCardStatus() (v map[string]string) {
	if !p.IsSetCardStatus() {
		return ChatMessage_CardStatus_DEFAULT
	}
	return p.CardStatus
}

var ChatMessage_ReasoningContent_DEFAULT string

func (p *ChatMessage) GetReasoningContent() (v string) {
	if !p.IsSetReasoningContent() {
		return ChatMessage_ReasoningContent_DEFAULT
	}
	return *p.ReasoningContent
}

var fieldIDToName_ChatMessage = map[int16]string{
	1:  "role",
	2:  "type",
	3:  "content",
	4:  "content_type",
	5:  "message_id",
	6:  "reply_id",
	7:  "section_id",
	8:  "extra_info",
	9:  "status",
	10: "broken_pos",
	11: "sender_id",
	12: "mention_list",
	13: "content_time",
	14: "message_index",
	15: "source",
	16: "reply_message",
	17: "required_action",
	18: "meta_infos",
	19: "card_status",
	20: "reasoning_content",
}

func (p *ChatMessage) IsSetExtraInfo() bool {
	return p.ExtraInfo != nil
}

func (p *ChatMessage) IsSetBrokenPos() bool {
	return p.BrokenPos != nil
}

func (p *ChatMessage) IsSetSenderID() bool {
	return p.SenderID != nil
}

func (p *ChatMessage) IsSetMentionList() bool {
	return p.MentionList != nil
}

func (p *ChatMessage) IsSetReplyMessage() bool {
	return p.ReplyMessage != nil
}

func (p *ChatMessage) IsSetRequiredAction() bool {
	return p.RequiredAction != nil
}

func (p *ChatMessage) IsSetMetaInfos() bool {
	return p.MetaInfos != nil
}

func (p *ChatMessage) IsSetCardStatus() bool {
	return p.CardStatus != nil
}

func (p *ChatMessage) IsSetReasoningContent() bool {
	return p.ReasoningContent != nil
}

func (p *ChatMessage) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChatMessage[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ChatMessage) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Role = _field
	return nil
}
func (p *ChatMessage) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}
func (p *ChatMessage) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *ChatMessage) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContentType = _field
	return nil
}
func (p *ChatMessage) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageID = _field
	return nil
}
func (p *ChatMessage) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReplyID = _field
	return nil
}
func (p *ChatMessage) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SectionID = _field
	return nil
}
func (p *ChatMessage) ReadField8(iprot thrift.TProtocol) error {
	_field := NewExtraInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ExtraInfo = _field
	return nil
}
func (p *ChatMessage) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Status = _field
	return nil
}
func (p *ChatMessage) ReadField10(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BrokenPos = _field
	return nil
}
func (p *ChatMessage) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SenderID = _field
	return nil
}
func (p *ChatMessage) ReadField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*MsgParticipantInfo, 0, size)
	values := make([]MsgParticipantInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MentionList = _field
	return nil
}
func (p *ChatMessage) ReadField13(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContentTime = _field
	return nil
}
func (p *ChatMessage) ReadField14(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageIndex = _field
	return nil
}
func (p *ChatMessage) ReadField15(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Source = _field
	return nil
}
func (p *ChatMessage) ReadField16(iprot thrift.TProtocol) error {
	_field := NewChatMessage()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ReplyMessage = _field
	return nil
}
func (p *ChatMessage) ReadField17(iprot thrift.TProtocol) error {
	_field := NewRequiredAction()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.RequiredAction = _field
	return nil
}
func (p *ChatMessage) ReadField18(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ChatMessageMetaInfo, 0, size)
	values := make([]ChatMessageMetaInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MetaInfos = _field
	return nil
}
func (p *ChatMessage) ReadField19(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.CardStatus = _field
	return nil
}
func (p *ChatMessage) ReadField20(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReasoningContent = _field
	return nil
}

func (p *ChatMessage) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ChatMessage"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChatMessage) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("role", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Role); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ChatMessage) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ChatMessage) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ChatMessage) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content_type", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ContentType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ChatMessage) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("message_id", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MessageID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ChatMessage) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("reply_id", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ReplyID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ChatMessage) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("section_id", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SectionID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *ChatMessage) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("extra_info", thrift.STRUCT, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ExtraInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *ChatMessage) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("status", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Status); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *ChatMessage) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetBrokenPos() {
		if err = oprot.WriteFieldBegin("broken_pos", thrift.I32, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.BrokenPos); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *ChatMessage) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetSenderID() {
		if err = oprot.WriteFieldBegin("sender_id", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SenderID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *ChatMessage) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetMentionList() {
		if err = oprot.WriteFieldBegin("mention_list", thrift.LIST, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MentionList)); err != nil {
			return err
		}
		for _, v := range p.MentionList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *ChatMessage) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content_time", thrift.I64, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ContentTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *ChatMessage) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("message_index", thrift.I64, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MessageIndex); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *ChatMessage) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("source", thrift.I32, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Source); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *ChatMessage) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetReplyMessage() {
		if err = oprot.WriteFieldBegin("reply_message", thrift.STRUCT, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ReplyMessage.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *ChatMessage) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetRequiredAction() {
		if err = oprot.WriteFieldBegin("required_action", thrift.STRUCT, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.RequiredAction.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}
func (p *ChatMessage) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetMetaInfos() {
		if err = oprot.WriteFieldBegin("meta_infos", thrift.LIST, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MetaInfos)); err != nil {
			return err
		}
		for _, v := range p.MetaInfos {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}
func (p *ChatMessage) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetCardStatus() {
		if err = oprot.WriteFieldBegin("card_status", thrift.MAP, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.CardStatus)); err != nil {
			return err
		}
		for k, v := range p.CardStatus {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}
func (p *ChatMessage) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetReasoningContent() {
		if err = oprot.WriteFieldBegin("reasoning_content", thrift.STRING, 20); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ReasoningContent); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}

func (p *ChatMessage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChatMessage(%+v)", *p)

}

type GetMessageListRequest struct {
	ConversationID string `thrift:"conversation_id,1" form:"conversation_id" json:"conversation_id" query:"conversation_id"`
	// First pass 0/-1, 0 - last page, -1 - unread first page
	Cursor    string `thrift:"cursor,2,required" form:"cursor,required" json:"cursor,required" query:"cursor,required"`
	Count     int32  `thrift:"count,3,required" form:"count,required" json:"count,required" query:"count,required"`
	BotID     string `thrift:"bot_id,4" form:"bot_id" json:"bot_id" query:"bot_id"`
	DraftMode *bool  `thrift:"draft_mode,5,optional" form:"draft_mode" json:"draft_mode,omitempty" query:"draft_mode"`
	// The bot template used
	PresetBot *string       `thrift:"preset_bot,6,optional" form:"preset_bot" json:"preset_bot,omitempty" query:"preset_bot"`
	Scene     *common.Scene `thrift:"scene,7,optional" form:"scene" json:"scene,omitempty" query:"scene"`
	// Different business situations under the same bot and uid
	BizKind *string `thrift:"biz_kind,8,optional" form:"biz_kind" json:"biz_kind,omitempty" query:"biz_kind"`
	// There are situations where you need to insert a chat before creating a chat history
	InsertHistoryMessageList []string       `thrift:"insert_history_message_list,9,optional" form:"insert_history_message_list" json:"insert_history_message_list,omitempty" query:"insert_history_message_list"`
	LoadDirection            *LoadDirection `thrift:"load_direction,10,optional" form:"load_direction" json:"load_direction,omitempty" query:"load_direction"`
	// Whether to force an appended message in an existing conversation
	MustAppend *bool `thrift:"must_append,11,optional" form:"must_append" json:"must_append,omitempty" query:"must_append"`
	// Share ID
	ShareID *int64 `thrift:"share_id,12,optional" json:"share_id,string" form:"share_id" query:"share_id"`
}

func NewGetMessageListRequest() *GetMessageListRequest {
	return &GetMessageListRequest{}
}

func (p *GetMessageListRequest) InitDefault() {
}

func (p *GetMessageListRequest) GetConversationID() (v string) {
	return p.ConversationID
}

func (p *GetMessageListRequest) GetCursor() (v string) {
	return p.Cursor
}

func (p *GetMessageListRequest) GetCount() (v int32) {
	return p.Count
}

func (p *GetMessageListRequest) GetBotID() (v string) {
	return p.BotID
}

var GetMessageListRequest_DraftMode_DEFAULT bool

func (p *GetMessageListRequest) GetDraftMode() (v bool) {
	if !p.IsSetDraftMode() {
		return GetMessageListRequest_DraftMode_DEFAULT
	}
	return *p.DraftMode
}

var GetMessageListRequest_PresetBot_DEFAULT string

func (p *GetMessageListRequest) GetPresetBot() (v string) {
	if !p.IsSetPresetBot() {
		return GetMessageListRequest_PresetBot_DEFAULT
	}
	return *p.PresetBot
}

var GetMessageListRequest_Scene_DEFAULT common.Scene

func (p *GetMessageListRequest) GetScene() (v common.Scene) {
	if !p.IsSetScene() {
		return GetMessageListRequest_Scene_DEFAULT
	}
	return *p.Scene
}

var GetMessageListRequest_BizKind_DEFAULT string

func (p *GetMessageListRequest) GetBizKind() (v string) {
	if !p.IsSetBizKind() {
		return GetMessageListRequest_BizKind_DEFAULT
	}
	return *p.BizKind
}

var GetMessageListRequest_InsertHistoryMessageList_DEFAULT []string

func (p *GetMessageListRequest) GetInsertHistoryMessageList() (v []string) {
	if !p.IsSetInsertHistoryMessageList() {
		return GetMessageListRequest_InsertHistoryMessageList_DEFAULT
	}
	return p.InsertHistoryMessageList
}

var GetMessageListRequest_LoadDirection_DEFAULT LoadDirection

func (p *GetMessageListRequest) GetLoadDirection() (v LoadDirection) {
	if !p.IsSetLoadDirection() {
		return GetMessageListRequest_LoadDirection_DEFAULT
	}
	return *p.LoadDirection
}

var GetMessageListRequest_MustAppend_DEFAULT bool

func (p *GetMessageListRequest) GetMustAppend() (v bool) {
	if !p.IsSetMustAppend() {
		return GetMessageListRequest_MustAppend_DEFAULT
	}
	return *p.MustAppend
}

var GetMessageListRequest_ShareID_DEFAULT int64

func (p *GetMessageListRequest) GetShareID() (v int64) {
	if !p.IsSetShareID() {
		return GetMessageListRequest_ShareID_DEFAULT
	}
	return *p.ShareID
}

var fieldIDToName_GetMessageListRequest = map[int16]string{
	1:  "conversation_id",
	2:  "cursor",
	3:  "count",
	4:  "bot_id",
	5:  "draft_mode",
	6:  "preset_bot",
	7:  "scene",
	8:  "biz_kind",
	9:  "insert_history_message_list",
	10: "load_direction",
	11: "must_append",
	12: "share_id",
}

func (p *GetMessageListRequest) IsSetDraftMode() bool {
	return p.DraftMode != nil
}

func (p *GetMessageListRequest) IsSetPresetBot() bool {
	return p.PresetBot != nil
}

func (p *GetMessageListRequest) IsSetScene() bool {
	return p.Scene != nil
}

func (p *GetMessageListRequest) IsSetBizKind() bool {
	return p.BizKind != nil
}

func (p *GetMessageListRequest) IsSetInsertHistoryMessageList() bool {
	return p.InsertHistoryMessageList != nil
}

func (p *GetMessageListRequest) IsSetLoadDirection() bool {
	return p.LoadDirection != nil
}

func (p *GetMessageListRequest) IsSetMustAppend() bool {
	return p.MustAppend != nil
}

func (p *GetMessageListRequest) IsSetShareID() bool {
	return p.ShareID != nil
}

func (p *GetMessageListRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCursor bool = false
	var issetCount bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCursor = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCursor {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetCount {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetMessageListRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetMessageListRequest[fieldId]))
}

func (p *GetMessageListRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConversationID = _field
	return nil
}
func (p *GetMessageListRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Cursor = _field
	return nil
}
func (p *GetMessageListRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Count = _field
	return nil
}
func (p *GetMessageListRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BotID = _field
	return nil
}
func (p *GetMessageListRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DraftMode = _field
	return nil
}
func (p *GetMessageListRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PresetBot = _field
	return nil
}
func (p *GetMessageListRequest) ReadField7(iprot thrift.TProtocol) error {

	var _field *common.Scene
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.Scene(v)
		_field = &tmp
	}
	p.Scene = _field
	return nil
}
func (p *GetMessageListRequest) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BizKind = _field
	return nil
}
func (p *GetMessageListRequest) ReadField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InsertHistoryMessageList = _field
	return nil
}
func (p *GetMessageListRequest) ReadField10(iprot thrift.TProtocol) error {

	var _field *LoadDirection
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LoadDirection(v)
		_field = &tmp
	}
	p.LoadDirection = _field
	return nil
}
func (p *GetMessageListRequest) ReadField11(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MustAppend = _field
	return nil
}
func (p *GetMessageListRequest) ReadField12(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShareID = _field
	return nil
}

func (p *GetMessageListRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetMessageListRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetMessageListRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("conversation_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ConversationID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetMessageListRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cursor", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Cursor); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetMessageListRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("count", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Count); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetMessageListRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot_id", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BotID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GetMessageListRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetDraftMode() {
		if err = oprot.WriteFieldBegin("draft_mode", thrift.BOOL, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.DraftMode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *GetMessageListRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPresetBot() {
		if err = oprot.WriteFieldBegin("preset_bot", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.PresetBot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *GetMessageListRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetScene() {
		if err = oprot.WriteFieldBegin("scene", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Scene)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *GetMessageListRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetBizKind() {
		if err = oprot.WriteFieldBegin("biz_kind", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BizKind); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *GetMessageListRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetInsertHistoryMessageList() {
		if err = oprot.WriteFieldBegin("insert_history_message_list", thrift.LIST, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.InsertHistoryMessageList)); err != nil {
			return err
		}
		for _, v := range p.InsertHistoryMessageList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *GetMessageListRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetLoadDirection() {
		if err = oprot.WriteFieldBegin("load_direction", thrift.I32, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LoadDirection)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *GetMessageListRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetMustAppend() {
		if err = oprot.WriteFieldBegin("must_append", thrift.BOOL, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.MustAppend); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *GetMessageListRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetShareID() {
		if err = oprot.WriteFieldBegin("share_id", thrift.I64, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ShareID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *GetMessageListRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMessageListRequest(%+v)", *p)

}

type GetMessageListResponse struct {
	MessageList []*ChatMessage `thrift:"message_list,1,required" form:"message_list,required" json:"message_list,required" query:"message_list,required"`
	// The position when the next brush exists (page up), opposite to the next_cursor page turning direction. Compatible with old logic, no prev prefix
	Cursor string `thrift:"cursor,2,required" form:"cursor,required" json:"cursor,required" query:"cursor,required"`
	// Whether the next swipe exists (page up), the opposite direction to the next_has_more page turning. Compatible with old logic, without prev prefix
	Hasmore        bool   `thrift:"hasmore,3,required" form:"hasmore,required" json:"hasmore,required" query:"hasmore,required"`
	ConversationID string `thrift:"conversation_id,4,required" form:"conversation_id,required" json:"conversation_id,required" query:"conversation_id,required"`
	// Session Latest section_id Only First Brush Back
	LastSectionID      *string                        `thrift:"last_section_id,5,optional" form:"last_section_id" json:"last_section_id,omitempty" query:"last_section_id"`
	Code               int64                          `thrift:"code,6" form:"code" json:"code" query:"code"`
	Msg                string                         `thrift:"msg,7" form:"msg" json:"msg" query:"msg"`
	ParticipantInfoMap map[string]*MsgParticipantInfo `thrift:"participant_info_map,8,optional" form:"participant_info_map" json:"participant_info_map,omitempty" query:"participant_info_map"`
	// The position when the next swipe exists (page down),
	NextCursor string `thrift:"next_cursor,9" form:"next_cursor" json:"next_cursor" query:"next_cursor"`
	// Does the next swipe exist (page down)
	NextHasMore      bool  `thrift:"next_has_more,10" form:"next_has_more" json:"next_has_more" query:"next_has_more"`
	ReadMessageIndex int64 `thrift:"read_message_index,11" json:"read_message_index,string" form:"read_message_index" query:"read_message_index"`
	//ID for botconnector
	ConnectorConversationID string `thrift:"connector_conversation_id,12" form:"connector_conversation_id" json:"connector_conversation_id" query:"connector_conversation_id"`
}

func NewGetMessageListResponse() *GetMessageListResponse {
	return &GetMessageListResponse{}
}

func (p *GetMessageListResponse) InitDefault() {
}

func (p *GetMessageListResponse) GetMessageList() (v []*ChatMessage) {
	return p.MessageList
}

func (p *GetMessageListResponse) GetCursor() (v string) {
	return p.Cursor
}

func (p *GetMessageListResponse) GetHasmore() (v bool) {
	return p.Hasmore
}

func (p *GetMessageListResponse) GetConversationID() (v string) {
	return p.ConversationID
}

var GetMessageListResponse_LastSectionID_DEFAULT string

func (p *GetMessageListResponse) GetLastSectionID() (v string) {
	if !p.IsSetLastSectionID() {
		return GetMessageListResponse_LastSectionID_DEFAULT
	}
	return *p.LastSectionID
}

func (p *GetMessageListResponse) GetCode() (v int64) {
	return p.Code
}

func (p *GetMessageListResponse) GetMsg() (v string) {
	return p.Msg
}

var GetMessageListResponse_ParticipantInfoMap_DEFAULT map[string]*MsgParticipantInfo

func (p *GetMessageListResponse) GetParticipantInfoMap() (v map[string]*MsgParticipantInfo) {
	if !p.IsSetParticipantInfoMap() {
		return GetMessageListResponse_ParticipantInfoMap_DEFAULT
	}
	return p.ParticipantInfoMap
}

func (p *GetMessageListResponse) GetNextCursor() (v string) {
	return p.NextCursor
}

func (p *GetMessageListResponse) GetNextHasMore() (v bool) {
	return p.NextHasMore
}

func (p *GetMessageListResponse) GetReadMessageIndex() (v int64) {
	return p.ReadMessageIndex
}

func (p *GetMessageListResponse) GetConnectorConversationID() (v string) {
	return p.ConnectorConversationID
}

var fieldIDToName_GetMessageListResponse = map[int16]string{
	1:  "message_list",
	2:  "cursor",
	3:  "hasmore",
	4:  "conversation_id",
	5:  "last_section_id",
	6:  "code",
	7:  "msg",
	8:  "participant_info_map",
	9:  "next_cursor",
	10: "next_has_more",
	11: "read_message_index",
	12: "connector_conversation_id",
}

func (p *GetMessageListResponse) IsSetLastSectionID() bool {
	return p.LastSectionID != nil
}

func (p *GetMessageListResponse) IsSetParticipantInfoMap() bool {
	return p.ParticipantInfoMap != nil
}

func (p *GetMessageListResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMessageList bool = false
	var issetCursor bool = false
	var issetHasmore bool = false
	var issetConversationID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCursor = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetHasmore = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetConversationID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMessageList {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCursor {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetHasmore {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetConversationID {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetMessageListResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetMessageListResponse[fieldId]))
}

func (p *GetMessageListResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ChatMessage, 0, size)
	values := make([]ChatMessage, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MessageList = _field
	return nil
}
func (p *GetMessageListResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Cursor = _field
	return nil
}
func (p *GetMessageListResponse) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Hasmore = _field
	return nil
}
func (p *GetMessageListResponse) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConversationID = _field
	return nil
}
func (p *GetMessageListResponse) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.LastSectionID = _field
	return nil
}
func (p *GetMessageListResponse) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *GetMessageListResponse) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *GetMessageListResponse) ReadField8(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]*MsgParticipantInfo, size)
	values := make([]MsgParticipantInfo, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		_val := &values[i]
		_val.InitDefault()
		if err := _val.Read(iprot); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.ParticipantInfoMap = _field
	return nil
}
func (p *GetMessageListResponse) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NextCursor = _field
	return nil
}
func (p *GetMessageListResponse) ReadField10(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NextHasMore = _field
	return nil
}
func (p *GetMessageListResponse) ReadField11(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReadMessageIndex = _field
	return nil
}
func (p *GetMessageListResponse) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnectorConversationID = _field
	return nil
}

func (p *GetMessageListResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetMessageListResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetMessageListResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("message_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MessageList)); err != nil {
		return err
	}
	for _, v := range p.MessageList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetMessageListResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cursor", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Cursor); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetMessageListResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("hasmore", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Hasmore); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetMessageListResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("conversation_id", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ConversationID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GetMessageListResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetLastSectionID() {
		if err = oprot.WriteFieldBegin("last_section_id", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.LastSectionID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *GetMessageListResponse) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *GetMessageListResponse) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *GetMessageListResponse) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetParticipantInfoMap() {
		if err = oprot.WriteFieldBegin("participant_info_map", thrift.MAP, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRUCT, len(p.ParticipantInfoMap)); err != nil {
			return err
		}
		for k, v := range p.ParticipantInfoMap {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *GetMessageListResponse) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("next_cursor", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NextCursor); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *GetMessageListResponse) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("next_has_more", thrift.BOOL, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.NextHasMore); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *GetMessageListResponse) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("read_message_index", thrift.I64, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ReadMessageIndex); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *GetMessageListResponse) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_conversation_id", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ConnectorConversationID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *GetMessageListResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMessageListResponse(%+v)", *p)

}

type DeleteMessageRequest struct {
	ConversationID int64         `thrift:"conversation_id,1,required" form:"conversation_id,required" json:"conversation_id,string,required" query:"conversation_id,required"`
	MessageID      int64         `thrift:"message_id,2,required" form:"message_id,required" json:"message_id,string,required" query:"message_id,required"`
	Scene          *common.Scene `thrift:"scene,3,optional" form:"scene" json:"scene,omitempty" query:"scene"`
	BotID          *int64        `thrift:"bot_id,4,optional" form:"bot_id" json:"bot_id,string,omitempty" query:"bot_id"`
}

func NewDeleteMessageRequest() *DeleteMessageRequest {
	return &DeleteMessageRequest{}
}

func (p *DeleteMessageRequest) InitDefault() {
}

func (p *DeleteMessageRequest) GetConversationID() (v int64) {
	return p.ConversationID
}

func (p *DeleteMessageRequest) GetMessageID() (v int64) {
	return p.MessageID
}

var DeleteMessageRequest_Scene_DEFAULT common.Scene

func (p *DeleteMessageRequest) GetScene() (v common.Scene) {
	if !p.IsSetScene() {
		return DeleteMessageRequest_Scene_DEFAULT
	}
	return *p.Scene
}

var DeleteMessageRequest_BotID_DEFAULT int64

func (p *DeleteMessageRequest) GetBotID() (v int64) {
	if !p.IsSetBotID() {
		return DeleteMessageRequest_BotID_DEFAULT
	}
	return *p.BotID
}

var fieldIDToName_DeleteMessageRequest = map[int16]string{
	1: "conversation_id",
	2: "message_id",
	3: "scene",
	4: "bot_id",
}

func (p *DeleteMessageRequest) IsSetScene() bool {
	return p.Scene != nil
}

func (p *DeleteMessageRequest) IsSetBotID() bool {
	return p.BotID != nil
}

func (p *DeleteMessageRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetConversationID bool = false
	var issetMessageID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetConversationID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessageID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetConversationID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMessageID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteMessageRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteMessageRequest[fieldId]))
}

func (p *DeleteMessageRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConversationID = _field
	return nil
}
func (p *DeleteMessageRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MessageID = _field
	return nil
}
func (p *DeleteMessageRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *common.Scene
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.Scene(v)
		_field = &tmp
	}
	p.Scene = _field
	return nil
}
func (p *DeleteMessageRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BotID = _field
	return nil
}

func (p *DeleteMessageRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteMessageRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteMessageRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("conversation_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ConversationID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DeleteMessageRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("message_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MessageID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DeleteMessageRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetScene() {
		if err = oprot.WriteFieldBegin("scene", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Scene)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *DeleteMessageRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetBotID() {
		if err = oprot.WriteFieldBegin("bot_id", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.BotID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DeleteMessageRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteMessageRequest(%+v)", *p)

}

type DeleteMessageResponse struct {
	Code int64  `thrift:"code,1" form:"code" json:"code" query:"code"`
	Msg  string `thrift:"msg,2" form:"msg" json:"msg" query:"msg"`
}

func NewDeleteMessageResponse() *DeleteMessageResponse {
	return &DeleteMessageResponse{}
}

func (p *DeleteMessageResponse) InitDefault() {
}

func (p *DeleteMessageResponse) GetCode() (v int64) {
	return p.Code
}

func (p *DeleteMessageResponse) GetMsg() (v string) {
	return p.Msg
}

var fieldIDToName_DeleteMessageResponse = map[int16]string{
	1: "code",
	2: "msg",
}

func (p *DeleteMessageResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteMessageResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteMessageResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *DeleteMessageResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}

func (p *DeleteMessageResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteMessageResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteMessageResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DeleteMessageResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteMessageResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteMessageResponse(%+v)", *p)

}

type BreakMessageRequest struct {
	//session id
	ConversationID int64 `thrift:"conversation_id,1,required" form:"conversation_id,required" json:"conversation_id,string,required" query:"conversation_id,required"`
	// Current issue id
	QueryMessageID int64 `thrift:"query_message_id,2,required" form:"query_message_id,required" json:"query_message_id,string,required" query:"query_message_id,required"`
	// Which reply was interrupted under the current question?
	AnswerMessageID *int64 `thrift:"answer_message_id,3,optional" form:"answer_message_id" json:"answer_message_id,string,omitempty" query:"answer_message_id"`
	// interrupt position
	BrokenPos *int32        `thrift:"broken_pos,4,optional" form:"broken_pos" json:"broken_pos,omitempty" query:"broken_pos"`
	Scene     *common.Scene `thrift:"scene,5,optional" form:"scene" json:"scene,omitempty" query:"scene"`
}

func NewBreakMessageRequest() *BreakMessageRequest {
	return &BreakMessageRequest{}
}

func (p *BreakMessageRequest) InitDefault() {
}

func (p *BreakMessageRequest) GetConversationID() (v int64) {
	return p.ConversationID
}

func (p *BreakMessageRequest) GetQueryMessageID() (v int64) {
	return p.QueryMessageID
}

var BreakMessageRequest_AnswerMessageID_DEFAULT int64

func (p *BreakMessageRequest) GetAnswerMessageID() (v int64) {
	if !p.IsSetAnswerMessageID() {
		return BreakMessageRequest_AnswerMessageID_DEFAULT
	}
	return *p.AnswerMessageID
}

var BreakMessageRequest_BrokenPos_DEFAULT int32

func (p *BreakMessageRequest) GetBrokenPos() (v int32) {
	if !p.IsSetBrokenPos() {
		return BreakMessageRequest_BrokenPos_DEFAULT
	}
	return *p.BrokenPos
}

var BreakMessageRequest_Scene_DEFAULT common.Scene

func (p *BreakMessageRequest) GetScene() (v common.Scene) {
	if !p.IsSetScene() {
		return BreakMessageRequest_Scene_DEFAULT
	}
	return *p.Scene
}

var fieldIDToName_BreakMessageRequest = map[int16]string{
	1: "conversation_id",
	2: "query_message_id",
	3: "answer_message_id",
	4: "broken_pos",
	5: "scene",
}

func (p *BreakMessageRequest) IsSetAnswerMessageID() bool {
	return p.AnswerMessageID != nil
}

func (p *BreakMessageRequest) IsSetBrokenPos() bool {
	return p.BrokenPos != nil
}

func (p *BreakMessageRequest) IsSetScene() bool {
	return p.Scene != nil
}

func (p *BreakMessageRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetConversationID bool = false
	var issetQueryMessageID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetConversationID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetQueryMessageID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetConversationID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetQueryMessageID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BreakMessageRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_BreakMessageRequest[fieldId]))
}

func (p *BreakMessageRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConversationID = _field
	return nil
}
func (p *BreakMessageRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.QueryMessageID = _field
	return nil
}
func (p *BreakMessageRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AnswerMessageID = _field
	return nil
}
func (p *BreakMessageRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BrokenPos = _field
	return nil
}
func (p *BreakMessageRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field *common.Scene
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.Scene(v)
		_field = &tmp
	}
	p.Scene = _field
	return nil
}

func (p *BreakMessageRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("BreakMessageRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BreakMessageRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("conversation_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ConversationID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *BreakMessageRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("query_message_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.QueryMessageID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *BreakMessageRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAnswerMessageID() {
		if err = oprot.WriteFieldBegin("answer_message_id", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.AnswerMessageID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *BreakMessageRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetBrokenPos() {
		if err = oprot.WriteFieldBegin("broken_pos", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.BrokenPos); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *BreakMessageRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetScene() {
		if err = oprot.WriteFieldBegin("scene", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Scene)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *BreakMessageRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BreakMessageRequest(%+v)", *p)

}

type BreakMessageResponse struct {
	Code int64  `thrift:"code,1" form:"code" json:"code" query:"code"`
	Msg  string `thrift:"msg,2" form:"msg" json:"msg" query:"msg"`
}

func NewBreakMessageResponse() *BreakMessageResponse {
	return &BreakMessageResponse{}
}

func (p *BreakMessageResponse) InitDefault() {
}

func (p *BreakMessageResponse) GetCode() (v int64) {
	return p.Code
}

func (p *BreakMessageResponse) GetMsg() (v string) {
	return p.Msg
}

var fieldIDToName_BreakMessageResponse = map[int16]string{
	1: "code",
	2: "msg",
}

func (p *BreakMessageResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BreakMessageResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *BreakMessageResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *BreakMessageResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}

func (p *BreakMessageResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("BreakMessageResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BreakMessageResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *BreakMessageResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *BreakMessageResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BreakMessageResponse(%+v)", *p)

}

// batch query
type ListMessageApiRequest struct {
	//session id
	ConversationID int64 `thrift:"conversation_id,1,required" json:"conversation_id,string,required" query:"conversation_id,required"`
	// limit number of entries
	Limit *int64 `thrift:"limit,2,optional" form:"limit" json:"limit,omitempty"`
	// Sort by desc/asc
	Order *string `thrift:"order,3,optional" form:"order" json:"order,omitempty"`
	//ID of a conversation
	ChatID *int64 `thrift:"chat_id,4,optional" form:"chat_id" json:"chat_id,string,omitempty"`
	// The ID you need to pass to turn the page forward.
	BeforeID *int64 `thrift:"before_id,5,optional" form:"before_id" json:"before_id,string,omitempty"`
	// Return the ID to be passed backwards.
	AfterID *int64     `thrift:"after_id,6,optional" form:"after_id" json:"after_id,string,omitempty"`
	Base    *base.Base `thrift:"Base,255" form:"Base" json:"Base" query:"Base"`
}

func NewListMessageApiRequest() *ListMessageApiRequest {
	return &ListMessageApiRequest{}
}

func (p *ListMessageApiRequest) InitDefault() {
}

func (p *ListMessageApiRequest) GetConversationID() (v int64) {
	return p.ConversationID
}

var ListMessageApiRequest_Limit_DEFAULT int64

func (p *ListMessageApiRequest) GetLimit() (v int64) {
	if !p.IsSetLimit() {
		return ListMessageApiRequest_Limit_DEFAULT
	}
	return *p.Limit
}

var ListMessageApiRequest_Order_DEFAULT string

func (p *ListMessageApiRequest) GetOrder() (v string) {
	if !p.IsSetOrder() {
		return ListMessageApiRequest_Order_DEFAULT
	}
	return *p.Order
}

var ListMessageApiRequest_ChatID_DEFAULT int64

func (p *ListMessageApiRequest) GetChatID() (v int64) {
	if !p.IsSetChatID() {
		return ListMessageApiRequest_ChatID_DEFAULT
	}
	return *p.ChatID
}

var ListMessageApiRequest_BeforeID_DEFAULT int64

func (p *ListMessageApiRequest) GetBeforeID() (v int64) {
	if !p.IsSetBeforeID() {
		return ListMessageApiRequest_BeforeID_DEFAULT
	}
	return *p.BeforeID
}

var ListMessageApiRequest_AfterID_DEFAULT int64

func (p *ListMessageApiRequest) GetAfterID() (v int64) {
	if !p.IsSetAfterID() {
		return ListMessageApiRequest_AfterID_DEFAULT
	}
	return *p.AfterID
}

var ListMessageApiRequest_Base_DEFAULT *base.Base

func (p *ListMessageApiRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ListMessageApiRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_ListMessageApiRequest = map[int16]string{
	1:   "conversation_id",
	2:   "limit",
	3:   "order",
	4:   "chat_id",
	5:   "before_id",
	6:   "after_id",
	255: "Base",
}

func (p *ListMessageApiRequest) IsSetLimit() bool {
	return p.Limit != nil
}

func (p *ListMessageApiRequest) IsSetOrder() bool {
	return p.Order != nil
}

func (p *ListMessageApiRequest) IsSetChatID() bool {
	return p.ChatID != nil
}

func (p *ListMessageApiRequest) IsSetBeforeID() bool {
	return p.BeforeID != nil
}

func (p *ListMessageApiRequest) IsSetAfterID() bool {
	return p.AfterID != nil
}

func (p *ListMessageApiRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *ListMessageApiRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetConversationID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetConversationID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetConversationID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListMessageApiRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListMessageApiRequest[fieldId]))
}

func (p *ListMessageApiRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConversationID = _field
	return nil
}
func (p *ListMessageApiRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Limit = _field
	return nil
}
func (p *ListMessageApiRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Order = _field
	return nil
}
func (p *ListMessageApiRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ChatID = _field
	return nil
}
func (p *ListMessageApiRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BeforeID = _field
	return nil
}
func (p *ListMessageApiRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AfterID = _field
	return nil
}
func (p *ListMessageApiRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ListMessageApiRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListMessageApiRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListMessageApiRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("conversation_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ConversationID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ListMessageApiRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetLimit() {
		if err = oprot.WriteFieldBegin("limit", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Limit); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ListMessageApiRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrder() {
		if err = oprot.WriteFieldBegin("order", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Order); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ListMessageApiRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetChatID() {
		if err = oprot.WriteFieldBegin("chat_id", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ChatID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ListMessageApiRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetBeforeID() {
		if err = oprot.WriteFieldBegin("before_id", thrift.I64, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.BeforeID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ListMessageApiRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetAfterID() {
		if err = oprot.WriteFieldBegin("after_id", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.AfterID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ListMessageApiRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ListMessageApiRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListMessageApiRequest(%+v)", *p)

}

type OpenMessageApi struct {
	// primary key ID
	ID int64 `thrift:"id,1" form:"id" json:"id,string" query:"id"`
	// agent id
	BotID int64 `thrift:"bot_id,2" form:"bot_id" json:"bot_id,string" query:"bot_id"`
	// user / assistant/tool
	Role string `thrift:"role,3" form:"role" json:"role" query:"role"`
	//message content
	Content string `thrift:"content,4" form:"content" json:"content" query:"content"`
	//session id
	ConversationID int64 `thrift:"conversation_id,5" form:"conversation_id" json:"conversation_id" query:"conversation_id"`
	// custom field
	MetaData map[string]string `thrift:"meta_data,6" form:"meta_data" json:"meta_data" query:"meta_data"`
	//creation time
	CreatedAt int64 `thrift:"created_at,7" form:"created_at" json:"created_at" query:"created_at"`
	//update time
	UpdatedAt int64 `thrift:"updated_at,8" form:"updated_at" json:"updated_at" query:"updated_at"`
	// ID of a conversation
	ChatID int64 `thrift:"chat_id,9" form:"chat_id" json:"chat_id" query:"chat_id"`
	// Content type, text/mix
	ContentType string `thrift:"content_type,10" form:"content_type" json:"content_type" query:"content_type"`
	//Message Type answer/question/function_call/tool_response
	Type string `thrift:"type,11" form:"type" json:"type" query:"type"`
	// The section_id of conversation
	SectionID string `thrift:"section_id,12" form:"section_id" json:"section_id" query:"section_id"`
	//Model Thinking Chain
	ReasoningContent *string `thrift:"reasoning_content,13,optional" form:"reasoning_content" json:"reasoning_content,omitempty" query:"reasoning_content"`
}

func NewOpenMessageApi() *OpenMessageApi {
	return &OpenMessageApi{}
}

func (p *OpenMessageApi) InitDefault() {
}

func (p *OpenMessageApi) GetID() (v int64) {
	return p.ID
}

func (p *OpenMessageApi) GetBotID() (v int64) {
	return p.BotID
}

func (p *OpenMessageApi) GetRole() (v string) {
	return p.Role
}

func (p *OpenMessageApi) GetContent() (v string) {
	return p.Content
}

func (p *OpenMessageApi) GetConversationID() (v int64) {
	return p.ConversationID
}

func (p *OpenMessageApi) GetMetaData() (v map[string]string) {
	return p.MetaData
}

func (p *OpenMessageApi) GetCreatedAt() (v int64) {
	return p.CreatedAt
}

func (p *OpenMessageApi) GetUpdatedAt() (v int64) {
	return p.UpdatedAt
}

func (p *OpenMessageApi) GetChatID() (v int64) {
	return p.ChatID
}

func (p *OpenMessageApi) GetContentType() (v string) {
	return p.ContentType
}

func (p *OpenMessageApi) GetType() (v string) {
	return p.Type
}

func (p *OpenMessageApi) GetSectionID() (v string) {
	return p.SectionID
}

var OpenMessageApi_ReasoningContent_DEFAULT string

func (p *OpenMessageApi) GetReasoningContent() (v string) {
	if !p.IsSetReasoningContent() {
		return OpenMessageApi_ReasoningContent_DEFAULT
	}
	return *p.ReasoningContent
}

var fieldIDToName_OpenMessageApi = map[int16]string{
	1:  "id",
	2:  "bot_id",
	3:  "role",
	4:  "content",
	5:  "conversation_id",
	6:  "meta_data",
	7:  "created_at",
	8:  "updated_at",
	9:  "chat_id",
	10: "content_type",
	11: "type",
	12: "section_id",
	13: "reasoning_content",
}

func (p *OpenMessageApi) IsSetReasoningContent() bool {
	return p.ReasoningContent != nil
}

func (p *OpenMessageApi) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OpenMessageApi[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OpenMessageApi) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *OpenMessageApi) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BotID = _field
	return nil
}
func (p *OpenMessageApi) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Role = _field
	return nil
}
func (p *OpenMessageApi) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Content = _field
	return nil
}
func (p *OpenMessageApi) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConversationID = _field
	return nil
}
func (p *OpenMessageApi) ReadField6(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.MetaData = _field
	return nil
}
func (p *OpenMessageApi) ReadField7(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatedAt = _field
	return nil
}
func (p *OpenMessageApi) ReadField8(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdatedAt = _field
	return nil
}
func (p *OpenMessageApi) ReadField9(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChatID = _field
	return nil
}
func (p *OpenMessageApi) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContentType = _field
	return nil
}
func (p *OpenMessageApi) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}
func (p *OpenMessageApi) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SectionID = _field
	return nil
}
func (p *OpenMessageApi) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReasoningContent = _field
	return nil
}

func (p *OpenMessageApi) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("OpenMessageApi"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OpenMessageApi) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *OpenMessageApi) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bot_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.BotID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *OpenMessageApi) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("role", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Role); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *OpenMessageApi) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Content); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *OpenMessageApi) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("conversation_id", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ConversationID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *OpenMessageApi) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("meta_data", thrift.MAP, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.MetaData)); err != nil {
		return err
	}
	for k, v := range p.MetaData {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *OpenMessageApi) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("created_at", thrift.I64, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreatedAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *OpenMessageApi) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("updated_at", thrift.I64, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UpdatedAt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *OpenMessageApi) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("chat_id", thrift.I64, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ChatID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *OpenMessageApi) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("content_type", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ContentType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *OpenMessageApi) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *OpenMessageApi) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("section_id", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SectionID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *OpenMessageApi) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetReasoningContent() {
		if err = oprot.WriteFieldBegin("reasoning_content", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ReasoningContent); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *OpenMessageApi) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenMessageApi(%+v)", *p)

}

type ListMessageApiResponse struct {
	Messages []*OpenMessageApi `thrift:"messages,1,optional" form:"data" json:"data,omitempty"`
	// Is there still data, true yes, false no
	HasMore *bool `thrift:"has_more,2,optional" form:"has_more" json:"has_more,omitempty"`
	// The ID of the first piece of data
	FirstID *int64 `thrift:"first_id,3,optional" form:"first_id" json:"first_id,string,omitempty"`
	// The id of the last piece of data.
	LastID *int64 `thrift:"last_id,4,optional" form:"last_id" json:"last_id,string,omitempty"`
}

func NewListMessageApiResponse() *ListMessageApiResponse {
	return &ListMessageApiResponse{}
}

func (p *ListMessageApiResponse) InitDefault() {
}

var ListMessageApiResponse_Messages_DEFAULT []*OpenMessageApi

func (p *ListMessageApiResponse) GetMessages() (v []*OpenMessageApi) {
	if !p.IsSetMessages() {
		return ListMessageApiResponse_Messages_DEFAULT
	}
	return p.Messages
}

var ListMessageApiResponse_HasMore_DEFAULT bool

func (p *ListMessageApiResponse) GetHasMore() (v bool) {
	if !p.IsSetHasMore() {
		return ListMessageApiResponse_HasMore_DEFAULT
	}
	return *p.HasMore
}

var ListMessageApiResponse_FirstID_DEFAULT int64

func (p *ListMessageApiResponse) GetFirstID() (v int64) {
	if !p.IsSetFirstID() {
		return ListMessageApiResponse_FirstID_DEFAULT
	}
	return *p.FirstID
}

var ListMessageApiResponse_LastID_DEFAULT int64

func (p *ListMessageApiResponse) GetLastID() (v int64) {
	if !p.IsSetLastID() {
		return ListMessageApiResponse_LastID_DEFAULT
	}
	return *p.LastID
}

var fieldIDToName_ListMessageApiResponse = map[int16]string{
	1: "messages",
	2: "has_more",
	3: "first_id",
	4: "last_id",
}

func (p *ListMessageApiResponse) IsSetMessages() bool {
	return p.Messages != nil
}

func (p *ListMessageApiResponse) IsSetHasMore() bool {
	return p.HasMore != nil
}

func (p *ListMessageApiResponse) IsSetFirstID() bool {
	return p.FirstID != nil
}

func (p *ListMessageApiResponse) IsSetLastID() bool {
	return p.LastID != nil
}

func (p *ListMessageApiResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListMessageApiResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ListMessageApiResponse) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*OpenMessageApi, 0, size)
	values := make([]OpenMessageApi, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Messages = _field
	return nil
}
func (p *ListMessageApiResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.HasMore = _field
	return nil
}
func (p *ListMessageApiResponse) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FirstID = _field
	return nil
}
func (p *ListMessageApiResponse) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.LastID = _field
	return nil
}

func (p *ListMessageApiResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListMessageApiResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListMessageApiResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessages() {
		if err = oprot.WriteFieldBegin("messages", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Messages)); err != nil {
			return err
		}
		for _, v := range p.Messages {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ListMessageApiResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetHasMore() {
		if err = oprot.WriteFieldBegin("has_more", thrift.BOOL, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.HasMore); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ListMessageApiResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetFirstID() {
		if err = oprot.WriteFieldBegin("first_id", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.FirstID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ListMessageApiResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetLastID() {
		if err = oprot.WriteFieldBegin("last_id", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.LastID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ListMessageApiResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListMessageApiResponse(%+v)", *p)

}
