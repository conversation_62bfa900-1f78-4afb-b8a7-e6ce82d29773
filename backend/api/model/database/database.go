// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package database

import (
	"context"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
	"github.com/coze-dev/coze-studio/backend/api/model/knowledge/document"
	"github.com/coze-dev/coze-studio/backend/api/model/table"
)

type DatabaseService interface {
	ListDatabase(ctx context.Context, req *table.ListDatabaseRequest) (r *table.ListDatabaseResponse, err error)

	GetDatabaseByID(ctx context.Context, req *table.SingleDatabaseRequest) (r *table.SingleDatabaseResponse, err error)

	AddDatabase(ctx context.Context, req *table.AddDatabaseRequest) (r *table.SingleDatabaseResponse, err error)

	UpdateDatabase(ctx context.Context, req *table.UpdateDatabaseRequest) (r *table.SingleDatabaseResponse, err error)

	DeleteDatabase(ctx context.Context, req *table.DeleteDatabaseRequest) (r *table.DeleteDatabaseResponse, err error)

	BindDatabase(ctx context.Context, req *table.BindDatabaseToBotRequest) (r *table.BindDatabaseToBotResponse, err error)

	UnBindDatabase(ctx context.Context, req *table.BindDatabaseToBotRequest) (r *table.BindDatabaseToBotResponse, err error)

	ListDatabaseRecords(ctx context.Context, req *table.ListDatabaseRecordsRequest) (r *table.ListDatabaseRecordsResponse, err error)

	UpdateDatabaseRecords(ctx context.Context, req *table.UpdateDatabaseRecordsRequest) (r *table.UpdateDatabaseRecordsResponse, err error)

	GetOnlineDatabaseId(ctx context.Context, req *table.GetOnlineDatabaseIdRequest) (r *table.GetOnlineDatabaseIdResponse, err error)

	ResetBotTable(ctx context.Context, req *table.ResetBotTableRequest) (r *table.ResetBotTableResponse, err error)

	GetDatabaseTemplate(ctx context.Context, req *table.GetDatabaseTemplateRequest) (r *table.GetDatabaseTemplateResponse, err error)

	GetConnectorName(ctx context.Context, req *table.GetSpaceConnectorListRequest) (r *table.GetSpaceConnectorListResponse, err error)

	GetBotDatabase(ctx context.Context, req *table.GetBotTableRequest) (r *table.GetBotTableResponse, err error)

	UpdateDatabaseBotSwitch(ctx context.Context, req *table.UpdateDatabaseBotSwitchRequest) (r *table.UpdateDatabaseBotSwitchResponse, err error)

	GetDatabaseTableSchema(ctx context.Context, req *table.GetTableSchemaRequest) (r *document.GetTableSchemaInfoResponse, err error)

	ValidateDatabaseTableSchema(ctx context.Context, req *table.ValidateTableSchemaRequest) (r *table.ValidateTableSchemaResponse, err error)

	SubmitDatabaseInsertTask(ctx context.Context, req *table.SubmitDatabaseInsertRequest) (r *table.SubmitDatabaseInsertResponse, err error)

	DatabaseFileProgressData(ctx context.Context, req *table.GetDatabaseFileProgressRequest) (r *table.GetDatabaseFileProgressResponse, err error)
}

type DatabaseServiceClient struct {
	c thrift.TClient
}

func NewDatabaseServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DatabaseServiceClient {
	return &DatabaseServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewDatabaseServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DatabaseServiceClient {
	return &DatabaseServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewDatabaseServiceClient(c thrift.TClient) *DatabaseServiceClient {
	return &DatabaseServiceClient{
		c: c,
	}
}

func (p *DatabaseServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *DatabaseServiceClient) ListDatabase(ctx context.Context, req *table.ListDatabaseRequest) (r *table.ListDatabaseResponse, err error) {
	var _args DatabaseServiceListDatabaseArgs
	_args.Req = req
	var _result DatabaseServiceListDatabaseResult
	if err = p.Client_().Call(ctx, "ListDatabase", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) GetDatabaseByID(ctx context.Context, req *table.SingleDatabaseRequest) (r *table.SingleDatabaseResponse, err error) {
	var _args DatabaseServiceGetDatabaseByIDArgs
	_args.Req = req
	var _result DatabaseServiceGetDatabaseByIDResult
	if err = p.Client_().Call(ctx, "GetDatabaseByID", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) AddDatabase(ctx context.Context, req *table.AddDatabaseRequest) (r *table.SingleDatabaseResponse, err error) {
	var _args DatabaseServiceAddDatabaseArgs
	_args.Req = req
	var _result DatabaseServiceAddDatabaseResult
	if err = p.Client_().Call(ctx, "AddDatabase", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) UpdateDatabase(ctx context.Context, req *table.UpdateDatabaseRequest) (r *table.SingleDatabaseResponse, err error) {
	var _args DatabaseServiceUpdateDatabaseArgs
	_args.Req = req
	var _result DatabaseServiceUpdateDatabaseResult
	if err = p.Client_().Call(ctx, "UpdateDatabase", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) DeleteDatabase(ctx context.Context, req *table.DeleteDatabaseRequest) (r *table.DeleteDatabaseResponse, err error) {
	var _args DatabaseServiceDeleteDatabaseArgs
	_args.Req = req
	var _result DatabaseServiceDeleteDatabaseResult
	if err = p.Client_().Call(ctx, "DeleteDatabase", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) BindDatabase(ctx context.Context, req *table.BindDatabaseToBotRequest) (r *table.BindDatabaseToBotResponse, err error) {
	var _args DatabaseServiceBindDatabaseArgs
	_args.Req = req
	var _result DatabaseServiceBindDatabaseResult
	if err = p.Client_().Call(ctx, "BindDatabase", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) UnBindDatabase(ctx context.Context, req *table.BindDatabaseToBotRequest) (r *table.BindDatabaseToBotResponse, err error) {
	var _args DatabaseServiceUnBindDatabaseArgs
	_args.Req = req
	var _result DatabaseServiceUnBindDatabaseResult
	if err = p.Client_().Call(ctx, "UnBindDatabase", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) ListDatabaseRecords(ctx context.Context, req *table.ListDatabaseRecordsRequest) (r *table.ListDatabaseRecordsResponse, err error) {
	var _args DatabaseServiceListDatabaseRecordsArgs
	_args.Req = req
	var _result DatabaseServiceListDatabaseRecordsResult
	if err = p.Client_().Call(ctx, "ListDatabaseRecords", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) UpdateDatabaseRecords(ctx context.Context, req *table.UpdateDatabaseRecordsRequest) (r *table.UpdateDatabaseRecordsResponse, err error) {
	var _args DatabaseServiceUpdateDatabaseRecordsArgs
	_args.Req = req
	var _result DatabaseServiceUpdateDatabaseRecordsResult
	if err = p.Client_().Call(ctx, "UpdateDatabaseRecords", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) GetOnlineDatabaseId(ctx context.Context, req *table.GetOnlineDatabaseIdRequest) (r *table.GetOnlineDatabaseIdResponse, err error) {
	var _args DatabaseServiceGetOnlineDatabaseIdArgs
	_args.Req = req
	var _result DatabaseServiceGetOnlineDatabaseIdResult
	if err = p.Client_().Call(ctx, "GetOnlineDatabaseId", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) ResetBotTable(ctx context.Context, req *table.ResetBotTableRequest) (r *table.ResetBotTableResponse, err error) {
	var _args DatabaseServiceResetBotTableArgs
	_args.Req = req
	var _result DatabaseServiceResetBotTableResult
	if err = p.Client_().Call(ctx, "ResetBotTable", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) GetDatabaseTemplate(ctx context.Context, req *table.GetDatabaseTemplateRequest) (r *table.GetDatabaseTemplateResponse, err error) {
	var _args DatabaseServiceGetDatabaseTemplateArgs
	_args.Req = req
	var _result DatabaseServiceGetDatabaseTemplateResult
	if err = p.Client_().Call(ctx, "GetDatabaseTemplate", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) GetConnectorName(ctx context.Context, req *table.GetSpaceConnectorListRequest) (r *table.GetSpaceConnectorListResponse, err error) {
	var _args DatabaseServiceGetConnectorNameArgs
	_args.Req = req
	var _result DatabaseServiceGetConnectorNameResult
	if err = p.Client_().Call(ctx, "GetConnectorName", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) GetBotDatabase(ctx context.Context, req *table.GetBotTableRequest) (r *table.GetBotTableResponse, err error) {
	var _args DatabaseServiceGetBotDatabaseArgs
	_args.Req = req
	var _result DatabaseServiceGetBotDatabaseResult
	if err = p.Client_().Call(ctx, "GetBotDatabase", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) UpdateDatabaseBotSwitch(ctx context.Context, req *table.UpdateDatabaseBotSwitchRequest) (r *table.UpdateDatabaseBotSwitchResponse, err error) {
	var _args DatabaseServiceUpdateDatabaseBotSwitchArgs
	_args.Req = req
	var _result DatabaseServiceUpdateDatabaseBotSwitchResult
	if err = p.Client_().Call(ctx, "UpdateDatabaseBotSwitch", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) GetDatabaseTableSchema(ctx context.Context, req *table.GetTableSchemaRequest) (r *document.GetTableSchemaInfoResponse, err error) {
	var _args DatabaseServiceGetDatabaseTableSchemaArgs
	_args.Req = req
	var _result DatabaseServiceGetDatabaseTableSchemaResult
	if err = p.Client_().Call(ctx, "GetDatabaseTableSchema", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) ValidateDatabaseTableSchema(ctx context.Context, req *table.ValidateTableSchemaRequest) (r *table.ValidateTableSchemaResponse, err error) {
	var _args DatabaseServiceValidateDatabaseTableSchemaArgs
	_args.Req = req
	var _result DatabaseServiceValidateDatabaseTableSchemaResult
	if err = p.Client_().Call(ctx, "ValidateDatabaseTableSchema", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) SubmitDatabaseInsertTask(ctx context.Context, req *table.SubmitDatabaseInsertRequest) (r *table.SubmitDatabaseInsertResponse, err error) {
	var _args DatabaseServiceSubmitDatabaseInsertTaskArgs
	_args.Req = req
	var _result DatabaseServiceSubmitDatabaseInsertTaskResult
	if err = p.Client_().Call(ctx, "SubmitDatabaseInsertTask", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatabaseServiceClient) DatabaseFileProgressData(ctx context.Context, req *table.GetDatabaseFileProgressRequest) (r *table.GetDatabaseFileProgressResponse, err error) {
	var _args DatabaseServiceDatabaseFileProgressDataArgs
	_args.Req = req
	var _result DatabaseServiceDatabaseFileProgressDataResult
	if err = p.Client_().Call(ctx, "DatabaseFileProgressData", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

type DatabaseServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      DatabaseService
}

func (p *DatabaseServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *DatabaseServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *DatabaseServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewDatabaseServiceProcessor(handler DatabaseService) *DatabaseServiceProcessor {
	self := &DatabaseServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self.AddToProcessorMap("ListDatabase", &databaseServiceProcessorListDatabase{handler: handler})
	self.AddToProcessorMap("GetDatabaseByID", &databaseServiceProcessorGetDatabaseByID{handler: handler})
	self.AddToProcessorMap("AddDatabase", &databaseServiceProcessorAddDatabase{handler: handler})
	self.AddToProcessorMap("UpdateDatabase", &databaseServiceProcessorUpdateDatabase{handler: handler})
	self.AddToProcessorMap("DeleteDatabase", &databaseServiceProcessorDeleteDatabase{handler: handler})
	self.AddToProcessorMap("BindDatabase", &databaseServiceProcessorBindDatabase{handler: handler})
	self.AddToProcessorMap("UnBindDatabase", &databaseServiceProcessorUnBindDatabase{handler: handler})
	self.AddToProcessorMap("ListDatabaseRecords", &databaseServiceProcessorListDatabaseRecords{handler: handler})
	self.AddToProcessorMap("UpdateDatabaseRecords", &databaseServiceProcessorUpdateDatabaseRecords{handler: handler})
	self.AddToProcessorMap("GetOnlineDatabaseId", &databaseServiceProcessorGetOnlineDatabaseId{handler: handler})
	self.AddToProcessorMap("ResetBotTable", &databaseServiceProcessorResetBotTable{handler: handler})
	self.AddToProcessorMap("GetDatabaseTemplate", &databaseServiceProcessorGetDatabaseTemplate{handler: handler})
	self.AddToProcessorMap("GetConnectorName", &databaseServiceProcessorGetConnectorName{handler: handler})
	self.AddToProcessorMap("GetBotDatabase", &databaseServiceProcessorGetBotDatabase{handler: handler})
	self.AddToProcessorMap("UpdateDatabaseBotSwitch", &databaseServiceProcessorUpdateDatabaseBotSwitch{handler: handler})
	self.AddToProcessorMap("GetDatabaseTableSchema", &databaseServiceProcessorGetDatabaseTableSchema{handler: handler})
	self.AddToProcessorMap("ValidateDatabaseTableSchema", &databaseServiceProcessorValidateDatabaseTableSchema{handler: handler})
	self.AddToProcessorMap("SubmitDatabaseInsertTask", &databaseServiceProcessorSubmitDatabaseInsertTask{handler: handler})
	self.AddToProcessorMap("DatabaseFileProgressData", &databaseServiceProcessorDatabaseFileProgressData{handler: handler})
	return self
}
func (p *DatabaseServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush(ctx)
	return false, x
}

type databaseServiceProcessorListDatabase struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorListDatabase) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceListDatabaseArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ListDatabase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceListDatabaseResult{}
	var retval *table.ListDatabaseResponse
	if retval, err2 = p.handler.ListDatabase(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ListDatabase: "+err2.Error())
		oprot.WriteMessageBegin("ListDatabase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ListDatabase", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorGetDatabaseByID struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorGetDatabaseByID) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceGetDatabaseByIDArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetDatabaseByID", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceGetDatabaseByIDResult{}
	var retval *table.SingleDatabaseResponse
	if retval, err2 = p.handler.GetDatabaseByID(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetDatabaseByID: "+err2.Error())
		oprot.WriteMessageBegin("GetDatabaseByID", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetDatabaseByID", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorAddDatabase struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorAddDatabase) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceAddDatabaseArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("AddDatabase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceAddDatabaseResult{}
	var retval *table.SingleDatabaseResponse
	if retval, err2 = p.handler.AddDatabase(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing AddDatabase: "+err2.Error())
		oprot.WriteMessageBegin("AddDatabase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("AddDatabase", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorUpdateDatabase struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorUpdateDatabase) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceUpdateDatabaseArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("UpdateDatabase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceUpdateDatabaseResult{}
	var retval *table.SingleDatabaseResponse
	if retval, err2 = p.handler.UpdateDatabase(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UpdateDatabase: "+err2.Error())
		oprot.WriteMessageBegin("UpdateDatabase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("UpdateDatabase", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorDeleteDatabase struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorDeleteDatabase) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceDeleteDatabaseArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("DeleteDatabase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceDeleteDatabaseResult{}
	var retval *table.DeleteDatabaseResponse
	if retval, err2 = p.handler.DeleteDatabase(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing DeleteDatabase: "+err2.Error())
		oprot.WriteMessageBegin("DeleteDatabase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("DeleteDatabase", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorBindDatabase struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorBindDatabase) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceBindDatabaseArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("BindDatabase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceBindDatabaseResult{}
	var retval *table.BindDatabaseToBotResponse
	if retval, err2 = p.handler.BindDatabase(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing BindDatabase: "+err2.Error())
		oprot.WriteMessageBegin("BindDatabase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("BindDatabase", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorUnBindDatabase struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorUnBindDatabase) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceUnBindDatabaseArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("UnBindDatabase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceUnBindDatabaseResult{}
	var retval *table.BindDatabaseToBotResponse
	if retval, err2 = p.handler.UnBindDatabase(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UnBindDatabase: "+err2.Error())
		oprot.WriteMessageBegin("UnBindDatabase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("UnBindDatabase", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorListDatabaseRecords struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorListDatabaseRecords) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceListDatabaseRecordsArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ListDatabaseRecords", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceListDatabaseRecordsResult{}
	var retval *table.ListDatabaseRecordsResponse
	if retval, err2 = p.handler.ListDatabaseRecords(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ListDatabaseRecords: "+err2.Error())
		oprot.WriteMessageBegin("ListDatabaseRecords", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ListDatabaseRecords", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorUpdateDatabaseRecords struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorUpdateDatabaseRecords) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceUpdateDatabaseRecordsArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("UpdateDatabaseRecords", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceUpdateDatabaseRecordsResult{}
	var retval *table.UpdateDatabaseRecordsResponse
	if retval, err2 = p.handler.UpdateDatabaseRecords(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UpdateDatabaseRecords: "+err2.Error())
		oprot.WriteMessageBegin("UpdateDatabaseRecords", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("UpdateDatabaseRecords", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorGetOnlineDatabaseId struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorGetOnlineDatabaseId) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceGetOnlineDatabaseIdArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetOnlineDatabaseId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceGetOnlineDatabaseIdResult{}
	var retval *table.GetOnlineDatabaseIdResponse
	if retval, err2 = p.handler.GetOnlineDatabaseId(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetOnlineDatabaseId: "+err2.Error())
		oprot.WriteMessageBegin("GetOnlineDatabaseId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetOnlineDatabaseId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorResetBotTable struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorResetBotTable) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceResetBotTableArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ResetBotTable", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceResetBotTableResult{}
	var retval *table.ResetBotTableResponse
	if retval, err2 = p.handler.ResetBotTable(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ResetBotTable: "+err2.Error())
		oprot.WriteMessageBegin("ResetBotTable", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ResetBotTable", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorGetDatabaseTemplate struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorGetDatabaseTemplate) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceGetDatabaseTemplateArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetDatabaseTemplate", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceGetDatabaseTemplateResult{}
	var retval *table.GetDatabaseTemplateResponse
	if retval, err2 = p.handler.GetDatabaseTemplate(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetDatabaseTemplate: "+err2.Error())
		oprot.WriteMessageBegin("GetDatabaseTemplate", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetDatabaseTemplate", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorGetConnectorName struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorGetConnectorName) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceGetConnectorNameArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetConnectorName", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceGetConnectorNameResult{}
	var retval *table.GetSpaceConnectorListResponse
	if retval, err2 = p.handler.GetConnectorName(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetConnectorName: "+err2.Error())
		oprot.WriteMessageBegin("GetConnectorName", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetConnectorName", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorGetBotDatabase struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorGetBotDatabase) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceGetBotDatabaseArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetBotDatabase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceGetBotDatabaseResult{}
	var retval *table.GetBotTableResponse
	if retval, err2 = p.handler.GetBotDatabase(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetBotDatabase: "+err2.Error())
		oprot.WriteMessageBegin("GetBotDatabase", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetBotDatabase", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorUpdateDatabaseBotSwitch struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorUpdateDatabaseBotSwitch) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceUpdateDatabaseBotSwitchArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("UpdateDatabaseBotSwitch", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceUpdateDatabaseBotSwitchResult{}
	var retval *table.UpdateDatabaseBotSwitchResponse
	if retval, err2 = p.handler.UpdateDatabaseBotSwitch(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UpdateDatabaseBotSwitch: "+err2.Error())
		oprot.WriteMessageBegin("UpdateDatabaseBotSwitch", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("UpdateDatabaseBotSwitch", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorGetDatabaseTableSchema struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorGetDatabaseTableSchema) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceGetDatabaseTableSchemaArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetDatabaseTableSchema", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceGetDatabaseTableSchemaResult{}
	var retval *document.GetTableSchemaInfoResponse
	if retval, err2 = p.handler.GetDatabaseTableSchema(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetDatabaseTableSchema: "+err2.Error())
		oprot.WriteMessageBegin("GetDatabaseTableSchema", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetDatabaseTableSchema", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorValidateDatabaseTableSchema struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorValidateDatabaseTableSchema) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceValidateDatabaseTableSchemaArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ValidateDatabaseTableSchema", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceValidateDatabaseTableSchemaResult{}
	var retval *table.ValidateTableSchemaResponse
	if retval, err2 = p.handler.ValidateDatabaseTableSchema(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ValidateDatabaseTableSchema: "+err2.Error())
		oprot.WriteMessageBegin("ValidateDatabaseTableSchema", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ValidateDatabaseTableSchema", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorSubmitDatabaseInsertTask struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorSubmitDatabaseInsertTask) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceSubmitDatabaseInsertTaskArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("SubmitDatabaseInsertTask", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceSubmitDatabaseInsertTaskResult{}
	var retval *table.SubmitDatabaseInsertResponse
	if retval, err2 = p.handler.SubmitDatabaseInsertTask(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing SubmitDatabaseInsertTask: "+err2.Error())
		oprot.WriteMessageBegin("SubmitDatabaseInsertTask", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("SubmitDatabaseInsertTask", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type databaseServiceProcessorDatabaseFileProgressData struct {
	handler DatabaseService
}

func (p *databaseServiceProcessorDatabaseFileProgressData) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatabaseServiceDatabaseFileProgressDataArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("DatabaseFileProgressData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatabaseServiceDatabaseFileProgressDataResult{}
	var retval *table.GetDatabaseFileProgressResponse
	if retval, err2 = p.handler.DatabaseFileProgressData(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing DatabaseFileProgressData: "+err2.Error())
		oprot.WriteMessageBegin("DatabaseFileProgressData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("DatabaseFileProgressData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type DatabaseServiceListDatabaseArgs struct {
	Req *table.ListDatabaseRequest `thrift:"req,1"`
}

func NewDatabaseServiceListDatabaseArgs() *DatabaseServiceListDatabaseArgs {
	return &DatabaseServiceListDatabaseArgs{}
}

func (p *DatabaseServiceListDatabaseArgs) InitDefault() {
}

var DatabaseServiceListDatabaseArgs_Req_DEFAULT *table.ListDatabaseRequest

func (p *DatabaseServiceListDatabaseArgs) GetReq() (v *table.ListDatabaseRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceListDatabaseArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceListDatabaseArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceListDatabaseArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceListDatabaseArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceListDatabaseArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceListDatabaseArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewListDatabaseRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceListDatabaseArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListDatabase_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceListDatabaseArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceListDatabaseArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceListDatabaseArgs(%+v)", *p)

}

type DatabaseServiceListDatabaseResult struct {
	Success *table.ListDatabaseResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceListDatabaseResult() *DatabaseServiceListDatabaseResult {
	return &DatabaseServiceListDatabaseResult{}
}

func (p *DatabaseServiceListDatabaseResult) InitDefault() {
}

var DatabaseServiceListDatabaseResult_Success_DEFAULT *table.ListDatabaseResponse

func (p *DatabaseServiceListDatabaseResult) GetSuccess() (v *table.ListDatabaseResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceListDatabaseResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceListDatabaseResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceListDatabaseResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceListDatabaseResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceListDatabaseResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceListDatabaseResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewListDatabaseResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceListDatabaseResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListDatabase_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceListDatabaseResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceListDatabaseResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceListDatabaseResult(%+v)", *p)

}

type DatabaseServiceGetDatabaseByIDArgs struct {
	Req *table.SingleDatabaseRequest `thrift:"req,1"`
}

func NewDatabaseServiceGetDatabaseByIDArgs() *DatabaseServiceGetDatabaseByIDArgs {
	return &DatabaseServiceGetDatabaseByIDArgs{}
}

func (p *DatabaseServiceGetDatabaseByIDArgs) InitDefault() {
}

var DatabaseServiceGetDatabaseByIDArgs_Req_DEFAULT *table.SingleDatabaseRequest

func (p *DatabaseServiceGetDatabaseByIDArgs) GetReq() (v *table.SingleDatabaseRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceGetDatabaseByIDArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceGetDatabaseByIDArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceGetDatabaseByIDArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceGetDatabaseByIDArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceGetDatabaseByIDArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseByIDArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewSingleDatabaseRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceGetDatabaseByIDArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDatabaseByID_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseByIDArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseByIDArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceGetDatabaseByIDArgs(%+v)", *p)

}

type DatabaseServiceGetDatabaseByIDResult struct {
	Success *table.SingleDatabaseResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceGetDatabaseByIDResult() *DatabaseServiceGetDatabaseByIDResult {
	return &DatabaseServiceGetDatabaseByIDResult{}
}

func (p *DatabaseServiceGetDatabaseByIDResult) InitDefault() {
}

var DatabaseServiceGetDatabaseByIDResult_Success_DEFAULT *table.SingleDatabaseResponse

func (p *DatabaseServiceGetDatabaseByIDResult) GetSuccess() (v *table.SingleDatabaseResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceGetDatabaseByIDResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceGetDatabaseByIDResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceGetDatabaseByIDResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceGetDatabaseByIDResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceGetDatabaseByIDResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseByIDResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewSingleDatabaseResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceGetDatabaseByIDResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDatabaseByID_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseByIDResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseByIDResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceGetDatabaseByIDResult(%+v)", *p)

}

type DatabaseServiceAddDatabaseArgs struct {
	Req *table.AddDatabaseRequest `thrift:"req,1"`
}

func NewDatabaseServiceAddDatabaseArgs() *DatabaseServiceAddDatabaseArgs {
	return &DatabaseServiceAddDatabaseArgs{}
}

func (p *DatabaseServiceAddDatabaseArgs) InitDefault() {
}

var DatabaseServiceAddDatabaseArgs_Req_DEFAULT *table.AddDatabaseRequest

func (p *DatabaseServiceAddDatabaseArgs) GetReq() (v *table.AddDatabaseRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceAddDatabaseArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceAddDatabaseArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceAddDatabaseArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceAddDatabaseArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceAddDatabaseArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceAddDatabaseArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewAddDatabaseRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceAddDatabaseArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("AddDatabase_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceAddDatabaseArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceAddDatabaseArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceAddDatabaseArgs(%+v)", *p)

}

type DatabaseServiceAddDatabaseResult struct {
	Success *table.SingleDatabaseResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceAddDatabaseResult() *DatabaseServiceAddDatabaseResult {
	return &DatabaseServiceAddDatabaseResult{}
}

func (p *DatabaseServiceAddDatabaseResult) InitDefault() {
}

var DatabaseServiceAddDatabaseResult_Success_DEFAULT *table.SingleDatabaseResponse

func (p *DatabaseServiceAddDatabaseResult) GetSuccess() (v *table.SingleDatabaseResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceAddDatabaseResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceAddDatabaseResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceAddDatabaseResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceAddDatabaseResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceAddDatabaseResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceAddDatabaseResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewSingleDatabaseResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceAddDatabaseResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("AddDatabase_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceAddDatabaseResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceAddDatabaseResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceAddDatabaseResult(%+v)", *p)

}

type DatabaseServiceUpdateDatabaseArgs struct {
	Req *table.UpdateDatabaseRequest `thrift:"req,1"`
}

func NewDatabaseServiceUpdateDatabaseArgs() *DatabaseServiceUpdateDatabaseArgs {
	return &DatabaseServiceUpdateDatabaseArgs{}
}

func (p *DatabaseServiceUpdateDatabaseArgs) InitDefault() {
}

var DatabaseServiceUpdateDatabaseArgs_Req_DEFAULT *table.UpdateDatabaseRequest

func (p *DatabaseServiceUpdateDatabaseArgs) GetReq() (v *table.UpdateDatabaseRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceUpdateDatabaseArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceUpdateDatabaseArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceUpdateDatabaseArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceUpdateDatabaseArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceUpdateDatabaseArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewUpdateDatabaseRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceUpdateDatabaseArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateDatabase_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceUpdateDatabaseArgs(%+v)", *p)

}

type DatabaseServiceUpdateDatabaseResult struct {
	Success *table.SingleDatabaseResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceUpdateDatabaseResult() *DatabaseServiceUpdateDatabaseResult {
	return &DatabaseServiceUpdateDatabaseResult{}
}

func (p *DatabaseServiceUpdateDatabaseResult) InitDefault() {
}

var DatabaseServiceUpdateDatabaseResult_Success_DEFAULT *table.SingleDatabaseResponse

func (p *DatabaseServiceUpdateDatabaseResult) GetSuccess() (v *table.SingleDatabaseResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceUpdateDatabaseResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceUpdateDatabaseResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceUpdateDatabaseResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceUpdateDatabaseResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceUpdateDatabaseResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewSingleDatabaseResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceUpdateDatabaseResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateDatabase_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceUpdateDatabaseResult(%+v)", *p)

}

type DatabaseServiceDeleteDatabaseArgs struct {
	Req *table.DeleteDatabaseRequest `thrift:"req,1"`
}

func NewDatabaseServiceDeleteDatabaseArgs() *DatabaseServiceDeleteDatabaseArgs {
	return &DatabaseServiceDeleteDatabaseArgs{}
}

func (p *DatabaseServiceDeleteDatabaseArgs) InitDefault() {
}

var DatabaseServiceDeleteDatabaseArgs_Req_DEFAULT *table.DeleteDatabaseRequest

func (p *DatabaseServiceDeleteDatabaseArgs) GetReq() (v *table.DeleteDatabaseRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceDeleteDatabaseArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceDeleteDatabaseArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceDeleteDatabaseArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceDeleteDatabaseArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceDeleteDatabaseArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceDeleteDatabaseArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewDeleteDatabaseRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceDeleteDatabaseArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteDatabase_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceDeleteDatabaseArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceDeleteDatabaseArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceDeleteDatabaseArgs(%+v)", *p)

}

type DatabaseServiceDeleteDatabaseResult struct {
	Success *table.DeleteDatabaseResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceDeleteDatabaseResult() *DatabaseServiceDeleteDatabaseResult {
	return &DatabaseServiceDeleteDatabaseResult{}
}

func (p *DatabaseServiceDeleteDatabaseResult) InitDefault() {
}

var DatabaseServiceDeleteDatabaseResult_Success_DEFAULT *table.DeleteDatabaseResponse

func (p *DatabaseServiceDeleteDatabaseResult) GetSuccess() (v *table.DeleteDatabaseResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceDeleteDatabaseResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceDeleteDatabaseResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceDeleteDatabaseResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceDeleteDatabaseResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceDeleteDatabaseResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceDeleteDatabaseResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewDeleteDatabaseResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceDeleteDatabaseResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteDatabase_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceDeleteDatabaseResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceDeleteDatabaseResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceDeleteDatabaseResult(%+v)", *p)

}

type DatabaseServiceBindDatabaseArgs struct {
	Req *table.BindDatabaseToBotRequest `thrift:"req,1"`
}

func NewDatabaseServiceBindDatabaseArgs() *DatabaseServiceBindDatabaseArgs {
	return &DatabaseServiceBindDatabaseArgs{}
}

func (p *DatabaseServiceBindDatabaseArgs) InitDefault() {
}

var DatabaseServiceBindDatabaseArgs_Req_DEFAULT *table.BindDatabaseToBotRequest

func (p *DatabaseServiceBindDatabaseArgs) GetReq() (v *table.BindDatabaseToBotRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceBindDatabaseArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceBindDatabaseArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceBindDatabaseArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceBindDatabaseArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceBindDatabaseArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceBindDatabaseArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewBindDatabaseToBotRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceBindDatabaseArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("BindDatabase_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceBindDatabaseArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceBindDatabaseArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceBindDatabaseArgs(%+v)", *p)

}

type DatabaseServiceBindDatabaseResult struct {
	Success *table.BindDatabaseToBotResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceBindDatabaseResult() *DatabaseServiceBindDatabaseResult {
	return &DatabaseServiceBindDatabaseResult{}
}

func (p *DatabaseServiceBindDatabaseResult) InitDefault() {
}

var DatabaseServiceBindDatabaseResult_Success_DEFAULT *table.BindDatabaseToBotResponse

func (p *DatabaseServiceBindDatabaseResult) GetSuccess() (v *table.BindDatabaseToBotResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceBindDatabaseResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceBindDatabaseResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceBindDatabaseResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceBindDatabaseResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceBindDatabaseResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceBindDatabaseResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewBindDatabaseToBotResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceBindDatabaseResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("BindDatabase_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceBindDatabaseResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceBindDatabaseResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceBindDatabaseResult(%+v)", *p)

}

type DatabaseServiceUnBindDatabaseArgs struct {
	Req *table.BindDatabaseToBotRequest `thrift:"req,1"`
}

func NewDatabaseServiceUnBindDatabaseArgs() *DatabaseServiceUnBindDatabaseArgs {
	return &DatabaseServiceUnBindDatabaseArgs{}
}

func (p *DatabaseServiceUnBindDatabaseArgs) InitDefault() {
}

var DatabaseServiceUnBindDatabaseArgs_Req_DEFAULT *table.BindDatabaseToBotRequest

func (p *DatabaseServiceUnBindDatabaseArgs) GetReq() (v *table.BindDatabaseToBotRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceUnBindDatabaseArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceUnBindDatabaseArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceUnBindDatabaseArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceUnBindDatabaseArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceUnBindDatabaseArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceUnBindDatabaseArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewBindDatabaseToBotRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceUnBindDatabaseArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UnBindDatabase_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceUnBindDatabaseArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceUnBindDatabaseArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceUnBindDatabaseArgs(%+v)", *p)

}

type DatabaseServiceUnBindDatabaseResult struct {
	Success *table.BindDatabaseToBotResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceUnBindDatabaseResult() *DatabaseServiceUnBindDatabaseResult {
	return &DatabaseServiceUnBindDatabaseResult{}
}

func (p *DatabaseServiceUnBindDatabaseResult) InitDefault() {
}

var DatabaseServiceUnBindDatabaseResult_Success_DEFAULT *table.BindDatabaseToBotResponse

func (p *DatabaseServiceUnBindDatabaseResult) GetSuccess() (v *table.BindDatabaseToBotResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceUnBindDatabaseResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceUnBindDatabaseResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceUnBindDatabaseResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceUnBindDatabaseResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceUnBindDatabaseResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceUnBindDatabaseResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewBindDatabaseToBotResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceUnBindDatabaseResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UnBindDatabase_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceUnBindDatabaseResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceUnBindDatabaseResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceUnBindDatabaseResult(%+v)", *p)

}

type DatabaseServiceListDatabaseRecordsArgs struct {
	Req *table.ListDatabaseRecordsRequest `thrift:"req,1"`
}

func NewDatabaseServiceListDatabaseRecordsArgs() *DatabaseServiceListDatabaseRecordsArgs {
	return &DatabaseServiceListDatabaseRecordsArgs{}
}

func (p *DatabaseServiceListDatabaseRecordsArgs) InitDefault() {
}

var DatabaseServiceListDatabaseRecordsArgs_Req_DEFAULT *table.ListDatabaseRecordsRequest

func (p *DatabaseServiceListDatabaseRecordsArgs) GetReq() (v *table.ListDatabaseRecordsRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceListDatabaseRecordsArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceListDatabaseRecordsArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceListDatabaseRecordsArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceListDatabaseRecordsArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceListDatabaseRecordsArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceListDatabaseRecordsArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewListDatabaseRecordsRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceListDatabaseRecordsArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListDatabaseRecords_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceListDatabaseRecordsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceListDatabaseRecordsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceListDatabaseRecordsArgs(%+v)", *p)

}

type DatabaseServiceListDatabaseRecordsResult struct {
	Success *table.ListDatabaseRecordsResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceListDatabaseRecordsResult() *DatabaseServiceListDatabaseRecordsResult {
	return &DatabaseServiceListDatabaseRecordsResult{}
}

func (p *DatabaseServiceListDatabaseRecordsResult) InitDefault() {
}

var DatabaseServiceListDatabaseRecordsResult_Success_DEFAULT *table.ListDatabaseRecordsResponse

func (p *DatabaseServiceListDatabaseRecordsResult) GetSuccess() (v *table.ListDatabaseRecordsResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceListDatabaseRecordsResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceListDatabaseRecordsResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceListDatabaseRecordsResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceListDatabaseRecordsResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceListDatabaseRecordsResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceListDatabaseRecordsResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewListDatabaseRecordsResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceListDatabaseRecordsResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListDatabaseRecords_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceListDatabaseRecordsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceListDatabaseRecordsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceListDatabaseRecordsResult(%+v)", *p)

}

type DatabaseServiceUpdateDatabaseRecordsArgs struct {
	Req *table.UpdateDatabaseRecordsRequest `thrift:"req,1"`
}

func NewDatabaseServiceUpdateDatabaseRecordsArgs() *DatabaseServiceUpdateDatabaseRecordsArgs {
	return &DatabaseServiceUpdateDatabaseRecordsArgs{}
}

func (p *DatabaseServiceUpdateDatabaseRecordsArgs) InitDefault() {
}

var DatabaseServiceUpdateDatabaseRecordsArgs_Req_DEFAULT *table.UpdateDatabaseRecordsRequest

func (p *DatabaseServiceUpdateDatabaseRecordsArgs) GetReq() (v *table.UpdateDatabaseRecordsRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceUpdateDatabaseRecordsArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceUpdateDatabaseRecordsArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceUpdateDatabaseRecordsArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceUpdateDatabaseRecordsArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceUpdateDatabaseRecordsArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseRecordsArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewUpdateDatabaseRecordsRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceUpdateDatabaseRecordsArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateDatabaseRecords_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseRecordsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseRecordsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceUpdateDatabaseRecordsArgs(%+v)", *p)

}

type DatabaseServiceUpdateDatabaseRecordsResult struct {
	Success *table.UpdateDatabaseRecordsResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceUpdateDatabaseRecordsResult() *DatabaseServiceUpdateDatabaseRecordsResult {
	return &DatabaseServiceUpdateDatabaseRecordsResult{}
}

func (p *DatabaseServiceUpdateDatabaseRecordsResult) InitDefault() {
}

var DatabaseServiceUpdateDatabaseRecordsResult_Success_DEFAULT *table.UpdateDatabaseRecordsResponse

func (p *DatabaseServiceUpdateDatabaseRecordsResult) GetSuccess() (v *table.UpdateDatabaseRecordsResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceUpdateDatabaseRecordsResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceUpdateDatabaseRecordsResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceUpdateDatabaseRecordsResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceUpdateDatabaseRecordsResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceUpdateDatabaseRecordsResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseRecordsResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewUpdateDatabaseRecordsResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceUpdateDatabaseRecordsResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateDatabaseRecords_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseRecordsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseRecordsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceUpdateDatabaseRecordsResult(%+v)", *p)

}

type DatabaseServiceGetOnlineDatabaseIdArgs struct {
	Req *table.GetOnlineDatabaseIdRequest `thrift:"req,1"`
}

func NewDatabaseServiceGetOnlineDatabaseIdArgs() *DatabaseServiceGetOnlineDatabaseIdArgs {
	return &DatabaseServiceGetOnlineDatabaseIdArgs{}
}

func (p *DatabaseServiceGetOnlineDatabaseIdArgs) InitDefault() {
}

var DatabaseServiceGetOnlineDatabaseIdArgs_Req_DEFAULT *table.GetOnlineDatabaseIdRequest

func (p *DatabaseServiceGetOnlineDatabaseIdArgs) GetReq() (v *table.GetOnlineDatabaseIdRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceGetOnlineDatabaseIdArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceGetOnlineDatabaseIdArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceGetOnlineDatabaseIdArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceGetOnlineDatabaseIdArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceGetOnlineDatabaseIdArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceGetOnlineDatabaseIdArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewGetOnlineDatabaseIdRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceGetOnlineDatabaseIdArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetOnlineDatabaseId_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceGetOnlineDatabaseIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceGetOnlineDatabaseIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceGetOnlineDatabaseIdArgs(%+v)", *p)

}

type DatabaseServiceGetOnlineDatabaseIdResult struct {
	Success *table.GetOnlineDatabaseIdResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceGetOnlineDatabaseIdResult() *DatabaseServiceGetOnlineDatabaseIdResult {
	return &DatabaseServiceGetOnlineDatabaseIdResult{}
}

func (p *DatabaseServiceGetOnlineDatabaseIdResult) InitDefault() {
}

var DatabaseServiceGetOnlineDatabaseIdResult_Success_DEFAULT *table.GetOnlineDatabaseIdResponse

func (p *DatabaseServiceGetOnlineDatabaseIdResult) GetSuccess() (v *table.GetOnlineDatabaseIdResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceGetOnlineDatabaseIdResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceGetOnlineDatabaseIdResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceGetOnlineDatabaseIdResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceGetOnlineDatabaseIdResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceGetOnlineDatabaseIdResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceGetOnlineDatabaseIdResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewGetOnlineDatabaseIdResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceGetOnlineDatabaseIdResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetOnlineDatabaseId_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceGetOnlineDatabaseIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceGetOnlineDatabaseIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceGetOnlineDatabaseIdResult(%+v)", *p)

}

type DatabaseServiceResetBotTableArgs struct {
	Req *table.ResetBotTableRequest `thrift:"req,1"`
}

func NewDatabaseServiceResetBotTableArgs() *DatabaseServiceResetBotTableArgs {
	return &DatabaseServiceResetBotTableArgs{}
}

func (p *DatabaseServiceResetBotTableArgs) InitDefault() {
}

var DatabaseServiceResetBotTableArgs_Req_DEFAULT *table.ResetBotTableRequest

func (p *DatabaseServiceResetBotTableArgs) GetReq() (v *table.ResetBotTableRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceResetBotTableArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceResetBotTableArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceResetBotTableArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceResetBotTableArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceResetBotTableArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceResetBotTableArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewResetBotTableRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceResetBotTableArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResetBotTable_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceResetBotTableArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceResetBotTableArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceResetBotTableArgs(%+v)", *p)

}

type DatabaseServiceResetBotTableResult struct {
	Success *table.ResetBotTableResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceResetBotTableResult() *DatabaseServiceResetBotTableResult {
	return &DatabaseServiceResetBotTableResult{}
}

func (p *DatabaseServiceResetBotTableResult) InitDefault() {
}

var DatabaseServiceResetBotTableResult_Success_DEFAULT *table.ResetBotTableResponse

func (p *DatabaseServiceResetBotTableResult) GetSuccess() (v *table.ResetBotTableResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceResetBotTableResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceResetBotTableResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceResetBotTableResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceResetBotTableResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceResetBotTableResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceResetBotTableResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewResetBotTableResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceResetBotTableResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResetBotTable_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceResetBotTableResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceResetBotTableResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceResetBotTableResult(%+v)", *p)

}

type DatabaseServiceGetDatabaseTemplateArgs struct {
	Req *table.GetDatabaseTemplateRequest `thrift:"req,1"`
}

func NewDatabaseServiceGetDatabaseTemplateArgs() *DatabaseServiceGetDatabaseTemplateArgs {
	return &DatabaseServiceGetDatabaseTemplateArgs{}
}

func (p *DatabaseServiceGetDatabaseTemplateArgs) InitDefault() {
}

var DatabaseServiceGetDatabaseTemplateArgs_Req_DEFAULT *table.GetDatabaseTemplateRequest

func (p *DatabaseServiceGetDatabaseTemplateArgs) GetReq() (v *table.GetDatabaseTemplateRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceGetDatabaseTemplateArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceGetDatabaseTemplateArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceGetDatabaseTemplateArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceGetDatabaseTemplateArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceGetDatabaseTemplateArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseTemplateArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewGetDatabaseTemplateRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceGetDatabaseTemplateArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDatabaseTemplate_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseTemplateArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseTemplateArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceGetDatabaseTemplateArgs(%+v)", *p)

}

type DatabaseServiceGetDatabaseTemplateResult struct {
	Success *table.GetDatabaseTemplateResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceGetDatabaseTemplateResult() *DatabaseServiceGetDatabaseTemplateResult {
	return &DatabaseServiceGetDatabaseTemplateResult{}
}

func (p *DatabaseServiceGetDatabaseTemplateResult) InitDefault() {
}

var DatabaseServiceGetDatabaseTemplateResult_Success_DEFAULT *table.GetDatabaseTemplateResponse

func (p *DatabaseServiceGetDatabaseTemplateResult) GetSuccess() (v *table.GetDatabaseTemplateResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceGetDatabaseTemplateResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceGetDatabaseTemplateResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceGetDatabaseTemplateResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceGetDatabaseTemplateResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceGetDatabaseTemplateResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseTemplateResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewGetDatabaseTemplateResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceGetDatabaseTemplateResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDatabaseTemplate_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseTemplateResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseTemplateResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceGetDatabaseTemplateResult(%+v)", *p)

}

type DatabaseServiceGetConnectorNameArgs struct {
	Req *table.GetSpaceConnectorListRequest `thrift:"req,1"`
}

func NewDatabaseServiceGetConnectorNameArgs() *DatabaseServiceGetConnectorNameArgs {
	return &DatabaseServiceGetConnectorNameArgs{}
}

func (p *DatabaseServiceGetConnectorNameArgs) InitDefault() {
}

var DatabaseServiceGetConnectorNameArgs_Req_DEFAULT *table.GetSpaceConnectorListRequest

func (p *DatabaseServiceGetConnectorNameArgs) GetReq() (v *table.GetSpaceConnectorListRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceGetConnectorNameArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceGetConnectorNameArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceGetConnectorNameArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceGetConnectorNameArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceGetConnectorNameArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceGetConnectorNameArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewGetSpaceConnectorListRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceGetConnectorNameArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetConnectorName_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceGetConnectorNameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceGetConnectorNameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceGetConnectorNameArgs(%+v)", *p)

}

type DatabaseServiceGetConnectorNameResult struct {
	Success *table.GetSpaceConnectorListResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceGetConnectorNameResult() *DatabaseServiceGetConnectorNameResult {
	return &DatabaseServiceGetConnectorNameResult{}
}

func (p *DatabaseServiceGetConnectorNameResult) InitDefault() {
}

var DatabaseServiceGetConnectorNameResult_Success_DEFAULT *table.GetSpaceConnectorListResponse

func (p *DatabaseServiceGetConnectorNameResult) GetSuccess() (v *table.GetSpaceConnectorListResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceGetConnectorNameResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceGetConnectorNameResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceGetConnectorNameResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceGetConnectorNameResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceGetConnectorNameResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceGetConnectorNameResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewGetSpaceConnectorListResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceGetConnectorNameResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetConnectorName_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceGetConnectorNameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceGetConnectorNameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceGetConnectorNameResult(%+v)", *p)

}

type DatabaseServiceGetBotDatabaseArgs struct {
	Req *table.GetBotTableRequest `thrift:"req,1"`
}

func NewDatabaseServiceGetBotDatabaseArgs() *DatabaseServiceGetBotDatabaseArgs {
	return &DatabaseServiceGetBotDatabaseArgs{}
}

func (p *DatabaseServiceGetBotDatabaseArgs) InitDefault() {
}

var DatabaseServiceGetBotDatabaseArgs_Req_DEFAULT *table.GetBotTableRequest

func (p *DatabaseServiceGetBotDatabaseArgs) GetReq() (v *table.GetBotTableRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceGetBotDatabaseArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceGetBotDatabaseArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceGetBotDatabaseArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceGetBotDatabaseArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceGetBotDatabaseArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceGetBotDatabaseArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewGetBotTableRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceGetBotDatabaseArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetBotDatabase_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceGetBotDatabaseArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceGetBotDatabaseArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceGetBotDatabaseArgs(%+v)", *p)

}

type DatabaseServiceGetBotDatabaseResult struct {
	Success *table.GetBotTableResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceGetBotDatabaseResult() *DatabaseServiceGetBotDatabaseResult {
	return &DatabaseServiceGetBotDatabaseResult{}
}

func (p *DatabaseServiceGetBotDatabaseResult) InitDefault() {
}

var DatabaseServiceGetBotDatabaseResult_Success_DEFAULT *table.GetBotTableResponse

func (p *DatabaseServiceGetBotDatabaseResult) GetSuccess() (v *table.GetBotTableResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceGetBotDatabaseResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceGetBotDatabaseResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceGetBotDatabaseResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceGetBotDatabaseResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceGetBotDatabaseResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceGetBotDatabaseResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewGetBotTableResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceGetBotDatabaseResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetBotDatabase_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceGetBotDatabaseResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceGetBotDatabaseResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceGetBotDatabaseResult(%+v)", *p)

}

type DatabaseServiceUpdateDatabaseBotSwitchArgs struct {
	Req *table.UpdateDatabaseBotSwitchRequest `thrift:"req,1"`
}

func NewDatabaseServiceUpdateDatabaseBotSwitchArgs() *DatabaseServiceUpdateDatabaseBotSwitchArgs {
	return &DatabaseServiceUpdateDatabaseBotSwitchArgs{}
}

func (p *DatabaseServiceUpdateDatabaseBotSwitchArgs) InitDefault() {
}

var DatabaseServiceUpdateDatabaseBotSwitchArgs_Req_DEFAULT *table.UpdateDatabaseBotSwitchRequest

func (p *DatabaseServiceUpdateDatabaseBotSwitchArgs) GetReq() (v *table.UpdateDatabaseBotSwitchRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceUpdateDatabaseBotSwitchArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceUpdateDatabaseBotSwitchArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceUpdateDatabaseBotSwitchArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceUpdateDatabaseBotSwitchArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceUpdateDatabaseBotSwitchArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseBotSwitchArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewUpdateDatabaseBotSwitchRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceUpdateDatabaseBotSwitchArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateDatabaseBotSwitch_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseBotSwitchArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseBotSwitchArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceUpdateDatabaseBotSwitchArgs(%+v)", *p)

}

type DatabaseServiceUpdateDatabaseBotSwitchResult struct {
	Success *table.UpdateDatabaseBotSwitchResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceUpdateDatabaseBotSwitchResult() *DatabaseServiceUpdateDatabaseBotSwitchResult {
	return &DatabaseServiceUpdateDatabaseBotSwitchResult{}
}

func (p *DatabaseServiceUpdateDatabaseBotSwitchResult) InitDefault() {
}

var DatabaseServiceUpdateDatabaseBotSwitchResult_Success_DEFAULT *table.UpdateDatabaseBotSwitchResponse

func (p *DatabaseServiceUpdateDatabaseBotSwitchResult) GetSuccess() (v *table.UpdateDatabaseBotSwitchResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceUpdateDatabaseBotSwitchResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceUpdateDatabaseBotSwitchResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceUpdateDatabaseBotSwitchResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceUpdateDatabaseBotSwitchResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceUpdateDatabaseBotSwitchResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseBotSwitchResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewUpdateDatabaseBotSwitchResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceUpdateDatabaseBotSwitchResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateDatabaseBotSwitch_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseBotSwitchResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceUpdateDatabaseBotSwitchResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceUpdateDatabaseBotSwitchResult(%+v)", *p)

}

type DatabaseServiceGetDatabaseTableSchemaArgs struct {
	Req *table.GetTableSchemaRequest `thrift:"req,1"`
}

func NewDatabaseServiceGetDatabaseTableSchemaArgs() *DatabaseServiceGetDatabaseTableSchemaArgs {
	return &DatabaseServiceGetDatabaseTableSchemaArgs{}
}

func (p *DatabaseServiceGetDatabaseTableSchemaArgs) InitDefault() {
}

var DatabaseServiceGetDatabaseTableSchemaArgs_Req_DEFAULT *table.GetTableSchemaRequest

func (p *DatabaseServiceGetDatabaseTableSchemaArgs) GetReq() (v *table.GetTableSchemaRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceGetDatabaseTableSchemaArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceGetDatabaseTableSchemaArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceGetDatabaseTableSchemaArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceGetDatabaseTableSchemaArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceGetDatabaseTableSchemaArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseTableSchemaArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewGetTableSchemaRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceGetDatabaseTableSchemaArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDatabaseTableSchema_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseTableSchemaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseTableSchemaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceGetDatabaseTableSchemaArgs(%+v)", *p)

}

type DatabaseServiceGetDatabaseTableSchemaResult struct {
	Success *document.GetTableSchemaInfoResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceGetDatabaseTableSchemaResult() *DatabaseServiceGetDatabaseTableSchemaResult {
	return &DatabaseServiceGetDatabaseTableSchemaResult{}
}

func (p *DatabaseServiceGetDatabaseTableSchemaResult) InitDefault() {
}

var DatabaseServiceGetDatabaseTableSchemaResult_Success_DEFAULT *document.GetTableSchemaInfoResponse

func (p *DatabaseServiceGetDatabaseTableSchemaResult) GetSuccess() (v *document.GetTableSchemaInfoResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceGetDatabaseTableSchemaResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceGetDatabaseTableSchemaResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceGetDatabaseTableSchemaResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceGetDatabaseTableSchemaResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceGetDatabaseTableSchemaResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseTableSchemaResult) ReadField0(iprot thrift.TProtocol) error {
	_field := document.NewGetTableSchemaInfoResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceGetDatabaseTableSchemaResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDatabaseTableSchema_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseTableSchemaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceGetDatabaseTableSchemaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceGetDatabaseTableSchemaResult(%+v)", *p)

}

type DatabaseServiceValidateDatabaseTableSchemaArgs struct {
	Req *table.ValidateTableSchemaRequest `thrift:"req,1"`
}

func NewDatabaseServiceValidateDatabaseTableSchemaArgs() *DatabaseServiceValidateDatabaseTableSchemaArgs {
	return &DatabaseServiceValidateDatabaseTableSchemaArgs{}
}

func (p *DatabaseServiceValidateDatabaseTableSchemaArgs) InitDefault() {
}

var DatabaseServiceValidateDatabaseTableSchemaArgs_Req_DEFAULT *table.ValidateTableSchemaRequest

func (p *DatabaseServiceValidateDatabaseTableSchemaArgs) GetReq() (v *table.ValidateTableSchemaRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceValidateDatabaseTableSchemaArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceValidateDatabaseTableSchemaArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceValidateDatabaseTableSchemaArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceValidateDatabaseTableSchemaArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceValidateDatabaseTableSchemaArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceValidateDatabaseTableSchemaArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewValidateTableSchemaRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceValidateDatabaseTableSchemaArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ValidateDatabaseTableSchema_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceValidateDatabaseTableSchemaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceValidateDatabaseTableSchemaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceValidateDatabaseTableSchemaArgs(%+v)", *p)

}

type DatabaseServiceValidateDatabaseTableSchemaResult struct {
	Success *table.ValidateTableSchemaResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceValidateDatabaseTableSchemaResult() *DatabaseServiceValidateDatabaseTableSchemaResult {
	return &DatabaseServiceValidateDatabaseTableSchemaResult{}
}

func (p *DatabaseServiceValidateDatabaseTableSchemaResult) InitDefault() {
}

var DatabaseServiceValidateDatabaseTableSchemaResult_Success_DEFAULT *table.ValidateTableSchemaResponse

func (p *DatabaseServiceValidateDatabaseTableSchemaResult) GetSuccess() (v *table.ValidateTableSchemaResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceValidateDatabaseTableSchemaResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceValidateDatabaseTableSchemaResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceValidateDatabaseTableSchemaResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceValidateDatabaseTableSchemaResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceValidateDatabaseTableSchemaResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceValidateDatabaseTableSchemaResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewValidateTableSchemaResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceValidateDatabaseTableSchemaResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ValidateDatabaseTableSchema_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceValidateDatabaseTableSchemaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceValidateDatabaseTableSchemaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceValidateDatabaseTableSchemaResult(%+v)", *p)

}

type DatabaseServiceSubmitDatabaseInsertTaskArgs struct {
	Req *table.SubmitDatabaseInsertRequest `thrift:"req,1"`
}

func NewDatabaseServiceSubmitDatabaseInsertTaskArgs() *DatabaseServiceSubmitDatabaseInsertTaskArgs {
	return &DatabaseServiceSubmitDatabaseInsertTaskArgs{}
}

func (p *DatabaseServiceSubmitDatabaseInsertTaskArgs) InitDefault() {
}

var DatabaseServiceSubmitDatabaseInsertTaskArgs_Req_DEFAULT *table.SubmitDatabaseInsertRequest

func (p *DatabaseServiceSubmitDatabaseInsertTaskArgs) GetReq() (v *table.SubmitDatabaseInsertRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceSubmitDatabaseInsertTaskArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceSubmitDatabaseInsertTaskArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceSubmitDatabaseInsertTaskArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceSubmitDatabaseInsertTaskArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceSubmitDatabaseInsertTaskArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceSubmitDatabaseInsertTaskArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewSubmitDatabaseInsertRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceSubmitDatabaseInsertTaskArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SubmitDatabaseInsertTask_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceSubmitDatabaseInsertTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceSubmitDatabaseInsertTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceSubmitDatabaseInsertTaskArgs(%+v)", *p)

}

type DatabaseServiceSubmitDatabaseInsertTaskResult struct {
	Success *table.SubmitDatabaseInsertResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceSubmitDatabaseInsertTaskResult() *DatabaseServiceSubmitDatabaseInsertTaskResult {
	return &DatabaseServiceSubmitDatabaseInsertTaskResult{}
}

func (p *DatabaseServiceSubmitDatabaseInsertTaskResult) InitDefault() {
}

var DatabaseServiceSubmitDatabaseInsertTaskResult_Success_DEFAULT *table.SubmitDatabaseInsertResponse

func (p *DatabaseServiceSubmitDatabaseInsertTaskResult) GetSuccess() (v *table.SubmitDatabaseInsertResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceSubmitDatabaseInsertTaskResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceSubmitDatabaseInsertTaskResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceSubmitDatabaseInsertTaskResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceSubmitDatabaseInsertTaskResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceSubmitDatabaseInsertTaskResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceSubmitDatabaseInsertTaskResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewSubmitDatabaseInsertResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceSubmitDatabaseInsertTaskResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SubmitDatabaseInsertTask_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceSubmitDatabaseInsertTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceSubmitDatabaseInsertTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceSubmitDatabaseInsertTaskResult(%+v)", *p)

}

type DatabaseServiceDatabaseFileProgressDataArgs struct {
	Req *table.GetDatabaseFileProgressRequest `thrift:"req,1"`
}

func NewDatabaseServiceDatabaseFileProgressDataArgs() *DatabaseServiceDatabaseFileProgressDataArgs {
	return &DatabaseServiceDatabaseFileProgressDataArgs{}
}

func (p *DatabaseServiceDatabaseFileProgressDataArgs) InitDefault() {
}

var DatabaseServiceDatabaseFileProgressDataArgs_Req_DEFAULT *table.GetDatabaseFileProgressRequest

func (p *DatabaseServiceDatabaseFileProgressDataArgs) GetReq() (v *table.GetDatabaseFileProgressRequest) {
	if !p.IsSetReq() {
		return DatabaseServiceDatabaseFileProgressDataArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatabaseServiceDatabaseFileProgressDataArgs = map[int16]string{
	1: "req",
}

func (p *DatabaseServiceDatabaseFileProgressDataArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatabaseServiceDatabaseFileProgressDataArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceDatabaseFileProgressDataArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceDatabaseFileProgressDataArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := table.NewGetDatabaseFileProgressRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatabaseServiceDatabaseFileProgressDataArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DatabaseFileProgressData_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceDatabaseFileProgressDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatabaseServiceDatabaseFileProgressDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceDatabaseFileProgressDataArgs(%+v)", *p)

}

type DatabaseServiceDatabaseFileProgressDataResult struct {
	Success *table.GetDatabaseFileProgressResponse `thrift:"success,0,optional"`
}

func NewDatabaseServiceDatabaseFileProgressDataResult() *DatabaseServiceDatabaseFileProgressDataResult {
	return &DatabaseServiceDatabaseFileProgressDataResult{}
}

func (p *DatabaseServiceDatabaseFileProgressDataResult) InitDefault() {
}

var DatabaseServiceDatabaseFileProgressDataResult_Success_DEFAULT *table.GetDatabaseFileProgressResponse

func (p *DatabaseServiceDatabaseFileProgressDataResult) GetSuccess() (v *table.GetDatabaseFileProgressResponse) {
	if !p.IsSetSuccess() {
		return DatabaseServiceDatabaseFileProgressDataResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatabaseServiceDatabaseFileProgressDataResult = map[int16]string{
	0: "success",
}

func (p *DatabaseServiceDatabaseFileProgressDataResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatabaseServiceDatabaseFileProgressDataResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatabaseServiceDatabaseFileProgressDataResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatabaseServiceDatabaseFileProgressDataResult) ReadField0(iprot thrift.TProtocol) error {
	_field := table.NewGetDatabaseFileProgressResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatabaseServiceDatabaseFileProgressDataResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DatabaseFileProgressData_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatabaseServiceDatabaseFileProgressDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatabaseServiceDatabaseFileProgressDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatabaseServiceDatabaseFileProgressDataResult(%+v)", *p)

}
