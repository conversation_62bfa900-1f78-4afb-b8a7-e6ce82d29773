/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by MockGen. DO NOT EDIT.
// Source: variable.go
//
// Generated by this command:
//
//	mockgen -destination varmock/var_mock.go --package mockvar -source variable.go
//

// Package mockvar is a generated GoMock package.
package mockvar

import (
	context "context"
	reflect "reflect"

	variable "github.com/coze-dev/coze-studio/backend/domain/workflow/crossdomain/variable"
	vo "github.com/coze-dev/coze-studio/backend/domain/workflow/entity/vo"
	compose "github.com/cloudwego/eino/compose"
	gomock "go.uber.org/mock/gomock"
)

// MockStore is a mock of Store interface.
type MockStore struct {
	ctrl     *gomock.Controller
	recorder *MockStoreMockRecorder
	isgomock struct{}
}

// MockStoreMockRecorder is the mock recorder for MockStore.
type MockStoreMockRecorder struct {
	mock *MockStore
}

// NewMockStore creates a new mock instance.
func NewMockStore(ctrl *gomock.Controller) *MockStore {
	mock := &MockStore{ctrl: ctrl}
	mock.recorder = &MockStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStore) EXPECT() *MockStoreMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockStore) Get(ctx context.Context, path compose.FieldPath, opts ...variable.OptionFn) (any, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, path}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].(any)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockStoreMockRecorder) Get(ctx, path any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, path}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockStore)(nil).Get), varargs...)
}

// Init mocks base method.
func (m *MockStore) Init(ctx context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Init", ctx)
}

// Init indicates an expected call of Init.
func (mr *MockStoreMockRecorder) Init(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Init", reflect.TypeOf((*MockStore)(nil).Init), ctx)
}

// Set mocks base method.
func (m *MockStore) Set(ctx context.Context, path compose.FieldPath, value any, opts ...variable.OptionFn) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, path, value}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Set", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Set indicates an expected call of Set.
func (mr *MockStoreMockRecorder) Set(ctx, path, value any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, path, value}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockStore)(nil).Set), varargs...)
}

// MockVariablesMetaGetter is a mock of VariablesMetaGetter interface.
type MockVariablesMetaGetter struct {
	ctrl     *gomock.Controller
	recorder *MockVariablesMetaGetterMockRecorder
	isgomock struct{}
}

// MockVariablesMetaGetterMockRecorder is the mock recorder for MockVariablesMetaGetter.
type MockVariablesMetaGetterMockRecorder struct {
	mock *MockVariablesMetaGetter
}

// NewMockVariablesMetaGetter creates a new mock instance.
func NewMockVariablesMetaGetter(ctrl *gomock.Controller) *MockVariablesMetaGetter {
	mock := &MockVariablesMetaGetter{ctrl: ctrl}
	mock.recorder = &MockVariablesMetaGetterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVariablesMetaGetter) EXPECT() *MockVariablesMetaGetterMockRecorder {
	return m.recorder
}

// GetAgentVariablesMeta mocks base method.
func (m *MockVariablesMetaGetter) GetAgentVariablesMeta(ctx context.Context, id int64, version string) (map[string]*vo.TypeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAgentVariablesMeta", ctx, id, version)
	ret0, _ := ret[0].(map[string]*vo.TypeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAgentVariablesMeta indicates an expected call of GetAgentVariablesMeta.
func (mr *MockVariablesMetaGetterMockRecorder) GetAgentVariablesMeta(ctx, id, version any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAgentVariablesMeta", reflect.TypeOf((*MockVariablesMetaGetter)(nil).GetAgentVariablesMeta), ctx, id, version)
}

// GetAppVariablesMeta mocks base method.
func (m *MockVariablesMetaGetter) GetAppVariablesMeta(ctx context.Context, id, version string) (map[string]*vo.TypeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppVariablesMeta", ctx, id, version)
	ret0, _ := ret[0].(map[string]*vo.TypeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppVariablesMeta indicates an expected call of GetAppVariablesMeta.
func (mr *MockVariablesMetaGetterMockRecorder) GetAppVariablesMeta(ctx, id, version any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppVariablesMeta", reflect.TypeOf((*MockVariablesMetaGetter)(nil).GetAppVariablesMeta), ctx, id, version)
}
