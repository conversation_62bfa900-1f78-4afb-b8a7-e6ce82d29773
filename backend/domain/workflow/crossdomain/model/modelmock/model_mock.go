// Code generated by MockGen. DO NOT EDIT.
// Source: model.go
//
// Generated by this command:
//
//	mockgen -destination modelmock/model_mock.go --package mockmodel -source model.go
//

// Package mockmodel is a generated GoMock package.
package mockmodel

import (
	context "context"
	reflect "reflect"

	model "github.com/cloudwego/eino/components/model"
	model0 "github.com/coze-dev/coze-studio/backend/domain/workflow/crossdomain/model"
	modelmgr "github.com/coze-dev/coze-studio/backend/infra/contract/modelmgr"
	gomock "go.uber.org/mock/gomock"
)

// MockManager is a mock of Manager interface.
type MockManager struct {
	ctrl     *gomock.Controller
	recorder *MockManagerMockRecorder
}

// MockManagerMockRecorder is the mock recorder for MockManager.
type MockManagerMockRecorder struct {
	mock *MockManager
}

// NewMockManager creates a new mock instance.
func NewMockManager(ctrl *gomock.Controller) *MockManager {
	mock := &MockManager{ctrl: ctrl}
	mock.recorder = &MockManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockManager) EXPECT() *MockManagerMockRecorder {
	return m.recorder
}

// GetModel mocks base method.
func (m *MockManager) GetModel(ctx context.Context, params *model0.LLMParams) (model.BaseChatModel, *modelmgr.Model, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetModel", ctx, params)
	ret0, _ := ret[0].(model.BaseChatModel)
	ret1, _ := ret[1].(*modelmgr.Model)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetModel indicates an expected call of GetModel.
func (mr *MockManagerMockRecorder) GetModel(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetModel", reflect.TypeOf((*MockManager)(nil).GetModel), ctx, params)
}
