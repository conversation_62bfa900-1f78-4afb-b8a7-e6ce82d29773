// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                        = new(Query)
	ConnectorWorkflowVersion *connectorWorkflowVersion
	NodeExecution            *nodeExecution
	WorkflowDraft            *workflowDraft
	WorkflowExecution        *workflowExecution
	WorkflowMeta             *workflowMeta
	WorkflowReference        *workflowReference
	WorkflowSnapshot         *workflowSnapshot
	WorkflowVersion          *workflowVersion
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	ConnectorWorkflowVersion = &Q.ConnectorWorkflowVersion
	NodeExecution = &Q.NodeExecution
	WorkflowDraft = &Q.WorkflowDraft
	WorkflowExecution = &Q.WorkflowExecution
	WorkflowMeta = &Q.WorkflowMeta
	WorkflowReference = &Q.WorkflowReference
	WorkflowSnapshot = &Q.WorkflowSnapshot
	WorkflowVersion = &Q.WorkflowVersion
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                       db,
		ConnectorWorkflowVersion: newConnectorWorkflowVersion(db, opts...),
		NodeExecution:            newNodeExecution(db, opts...),
		WorkflowDraft:            newWorkflowDraft(db, opts...),
		WorkflowExecution:        newWorkflowExecution(db, opts...),
		WorkflowMeta:             newWorkflowMeta(db, opts...),
		WorkflowReference:        newWorkflowReference(db, opts...),
		WorkflowSnapshot:         newWorkflowSnapshot(db, opts...),
		WorkflowVersion:          newWorkflowVersion(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	ConnectorWorkflowVersion connectorWorkflowVersion
	NodeExecution            nodeExecution
	WorkflowDraft            workflowDraft
	WorkflowExecution        workflowExecution
	WorkflowMeta             workflowMeta
	WorkflowReference        workflowReference
	WorkflowSnapshot         workflowSnapshot
	WorkflowVersion          workflowVersion
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                       db,
		ConnectorWorkflowVersion: q.ConnectorWorkflowVersion.clone(db),
		NodeExecution:            q.NodeExecution.clone(db),
		WorkflowDraft:            q.WorkflowDraft.clone(db),
		WorkflowExecution:        q.WorkflowExecution.clone(db),
		WorkflowMeta:             q.WorkflowMeta.clone(db),
		WorkflowReference:        q.WorkflowReference.clone(db),
		WorkflowSnapshot:         q.WorkflowSnapshot.clone(db),
		WorkflowVersion:          q.WorkflowVersion.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                       db,
		ConnectorWorkflowVersion: q.ConnectorWorkflowVersion.replaceDB(db),
		NodeExecution:            q.NodeExecution.replaceDB(db),
		WorkflowDraft:            q.WorkflowDraft.replaceDB(db),
		WorkflowExecution:        q.WorkflowExecution.replaceDB(db),
		WorkflowMeta:             q.WorkflowMeta.replaceDB(db),
		WorkflowReference:        q.WorkflowReference.replaceDB(db),
		WorkflowSnapshot:         q.WorkflowSnapshot.replaceDB(db),
		WorkflowVersion:          q.WorkflowVersion.replaceDB(db),
	}
}

type queryCtx struct {
	ConnectorWorkflowVersion IConnectorWorkflowVersionDo
	NodeExecution            INodeExecutionDo
	WorkflowDraft            IWorkflowDraftDo
	WorkflowExecution        IWorkflowExecutionDo
	WorkflowMeta             IWorkflowMetaDo
	WorkflowReference        IWorkflowReferenceDo
	WorkflowSnapshot         IWorkflowSnapshotDo
	WorkflowVersion          IWorkflowVersionDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		ConnectorWorkflowVersion: q.ConnectorWorkflowVersion.WithContext(ctx),
		NodeExecution:            q.NodeExecution.WithContext(ctx),
		WorkflowDraft:            q.WorkflowDraft.WithContext(ctx),
		WorkflowExecution:        q.WorkflowExecution.WithContext(ctx),
		WorkflowMeta:             q.WorkflowMeta.WithContext(ctx),
		WorkflowReference:        q.WorkflowReference.WithContext(ctx),
		WorkflowSnapshot:         q.WorkflowSnapshot.WithContext(ctx),
		WorkflowVersion:          q.WorkflowVersion.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
