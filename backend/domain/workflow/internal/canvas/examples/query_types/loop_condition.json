{"nodes": [{"id": "100001", "type": "1", "meta": {"position": {"x": -377, "y": -245}}, "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"type": "string", "name": "input", "required": false}], "trigger_parameters": [{"type": "string", "name": "input", "required": false}]}}, {"id": "900001", "type": "2", "meta": {"position": {"x": 1375.0000457763672, "y": -82.30000000000001}}, "data": {"nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}, "inputs": {"terminatePlan": "returnVariables", "inputParameters": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "input"}, "rawMeta": {"type": 1}}}}]}}}, {"id": "119585", "type": "21", "meta": {"position": {"x": 364.0000457763672, "y": -363.5}, "canvasPosition": {"x": 163.0000457763672, "y": -32.70000000000002}}, "data": {"nodeMeta": {"title": "循环", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Loop-v2.jpg", "description": "用于通过设定循环次数和逻辑，重复执行一系列任务", "mainColor": "#00B2B2", "subTitle": "循环"}, "inputs": {"loopType": "array", "loopCount": {"type": "integer", "value": {"type": "literal", "content": "10"}}, "variableParameters": [{"name": "v1", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "global_variable_user", "path": ["user_v1"], "blockID": "", "name": ""}, "rawMeta": {"type": 1}}}}], "inputParameters": [{"name": "input", "input": {"type": "list", "schema": {"type": "boolean"}, "value": {"type": "ref", "content": {"source": "global_variable_app", "path": ["app_v1"], "blockID": "", "name": ""}, "rawMeta": {"type": 101}}}}]}, "outputs": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "119585", "name": "v1"}, "rawMeta": {"type": 1}}}}]}, "blocks": [{"id": "114884", "type": "5", "meta": {"position": {"x": 0, "y": 100}}, "data": {"nodeMeta": {"title": "代码", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "description": "编写代码，处理输入变量来生成返回值", "mainColor": "#00B2B2", "subTitle": "代码"}, "inputs": {"inputParameters": [{"name": "input", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "input"}, "rawMeta": {"type": 1}}}}], "code": "// 在这里，您可以通过 ‘params’  获取节点中的输入变量，并通过 'ret' 输出结果\n// 'params' 和 'ret' 已经被正确地注入到环境中\n// 下面是一个示例，获取节点输入中参数名为‘input’的值：\n// const input = params.input; \n// 下面是一个示例，输出一个包含多种数据类型的 'ret' 对象：\n// const ret = { \"name\": ‘小明’, \"hobbies\": [“看书”, “旅游”] };\n\nasync function main({ params }: Args): Promise<Output> {\n    // 构建输出对象\n    const ret = {\n        \"key0\": params.input + params.input, // 拼接两次入参 input 的值\n        \"key1\": [\"hello\", \"world\"], // 输出一个数组\n        \"key2\": { // 输出一个Object\n            \"key21\": \"hi\"\n        },\n    };\n\n    return ret;\n}", "language": 5, "settingOnError": {"processType": 1, "timeoutMs": 60000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "key0"}, {"type": "list", "name": "key1", "schema": {"type": "string"}}, {"type": "object", "name": "key2", "schema": [{"type": "string", "name": "key21"}]}]}}], "edges": [{"sourceNodeID": "119585", "targetNodeID": "114884", "sourcePortID": "loop-function-inline-output"}, {"sourceNodeID": "114884", "targetNodeID": "119585", "targetPortID": "loop-function-inline-input"}]}, {"id": "170824", "type": "8", "meta": {"position": {"x": 664.0000457763672, "y": -129.9}}, "data": {"nodeMeta": {"title": "选择器", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Condition-v2.jpg", "description": "连接多个下游分支，若设定的条件成立则仅运行对应的分支，若均不成立则只运行“否则”分支", "mainColor": "#00B2B2", "subTitle": "选择器"}, "inputs": {"branches": [{"condition": {"logic": 2, "conditions": [{"operator": 1, "left": {"input": {"type": "string", "value": {"type": "ref", "content": {"source": "global_variable_user", "path": ["user_v1"], "blockID": "", "name": ""}}}}, "right": {"input": {"type": "string", "value": {"type": "literal", "content": "123", "rawMeta": {"type": 1}}}}}]}}]}}}, {"id": "143932", "type": "5", "meta": {"position": {"x": 967, "y": 140}}, "data": {"nodeMeta": {"title": "代码_1", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "description": "编写代码，处理输入变量来生成返回值", "mainColor": "#00B2B2", "subTitle": "代码"}, "inputs": {"inputParameters": [{"name": "input", "input": {"type": "string", "value": {"type": "literal", "content": "123", "rawMeta": {"type": 1}}}}], "code": "// 在这里，您可以通过 ‘params’  获取节点中的输入变量，并通过 'ret' 输出结果\n// 'params' 和 'ret' 已经被正确地注入到环境中\n// 下面是一个示例，获取节点输入中参数名为‘input’的值：\n// const input = params.input; \n// 下面是一个示例，输出一个包含多种数据类型的 'ret' 对象：\n// const ret = { \"name\": ‘小明’, \"hobbies\": [“看书”, “旅游”] };\n\nasync function main({ params }: Args): Promise<Output> {\n    // 构建输出对象\n    const ret = {\n        \"key0\": params.input + params.input, // 拼接两次入参 input 的值\n        \"key1\": [\"hello\", \"world\"], // 输出一个数组\n        \"key2\": { // 输出一个Object\n            \"key21\": \"hi\"\n        },\n    };\n\n    return ret;\n}", "language": 5, "settingOnError": {"processType": 1, "timeoutMs": 60000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "key0"}, {"type": "list", "name": "key1", "schema": {"type": "string"}}, {"type": "object", "name": "key2", "schema": [{"type": "string", "name": "key21"}]}]}}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "119585"}, {"sourceNodeID": "170824", "targetNodeID": "900001", "sourcePortID": "true"}, {"sourceNodeID": "143932", "targetNodeID": "900001"}, {"sourceNodeID": "119585", "targetNodeID": "170824", "sourcePortID": "loop-output"}, {"sourceNodeID": "170824", "targetNodeID": "143932", "sourcePortID": "false"}], "versions": {"loop": "v2"}}