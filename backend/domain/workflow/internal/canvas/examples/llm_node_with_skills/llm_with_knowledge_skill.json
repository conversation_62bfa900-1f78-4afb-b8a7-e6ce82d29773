{"nodes": [{"id": "100001", "type": "1", "meta": {"position": {"x": -324.6903950582028, "y": -542.5290216685637}}, "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"type": "string", "name": "input", "required": false}], "trigger_parameters": [{"type": "string", "name": "input", "required": false}]}}, {"id": "900001", "type": "2", "meta": {"position": {"x": 582.312213137821, "y": -738.3403723783939}}, "data": {"nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}, "inputs": {"terminatePlan": "returnVariables", "inputParameters": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "132728", "name": "output"}, "rawMeta": {"type": 1}}}}]}}}, {"id": "132728", "type": "3", "meta": {"position": {"x": 35.30960494179719, "y": -791.7403723783939}}, "data": {"nodeMeta": {"title": "大模型", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "description": "调用大语言模型,使用变量和提示词生成回复", "mainColor": "#5C62FF", "subTitle": "大模型"}, "inputs": {"inputParameters": [{"name": "input", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "input"}, "rawMeta": {"type": 1}}}}], "llmParam": [{"name": "modelType", "input": {"type": "integer", "value": {"type": "literal", "content": "1", "rawMeta": {"type": 2}}}}, {"name": "modleName", "input": {"type": "string", "value": {"type": "literal", "content": "豆包·1.5·Pro·32k", "rawMeta": {"type": 1}}}}, {"name": "generationDiversity", "input": {"type": "string", "value": {"type": "literal", "content": "balance", "rawMeta": {"type": 1}}}}, {"name": "temperature", "input": {"type": "float", "value": {"type": "literal", "content": "0.8", "rawMeta": {"type": 4}}}}, {"name": "maxTokens", "input": {"type": "integer", "value": {"type": "literal", "content": "4096", "rawMeta": {"type": 2}}}}, {"name": "responseFormat", "input": {"type": "integer", "value": {"type": "literal", "content": "0", "rawMeta": {"type": 2}}}}, {"name": "prompt", "input": {"type": "string", "value": {"type": "literal", "content": "{{input}}", "rawMeta": {"type": 1}}}}, {"name": "enableChatHistory", "input": {"type": "boolean", "value": {"type": "literal", "content": false, "rawMeta": {"type": 3}}}}, {"name": "chatHistoryRound", "input": {"type": "integer", "value": {"type": "literal", "content": "3", "rawMeta": {"type": 2}}}}, {"name": "systemPrompt", "input": {"type": "string", "value": {"type": "literal", "content": "你是一个旅游推荐专家，通过用户提出的问题，推荐用户具体城市的旅游景点", "rawMeta": {"type": 1}}}}], "fcParam": {"knowledgeFCParam": {"knowledgeList": [{"id": "7512369185624686592", "name": "旅游景点"}], "global_setting": {"auto": false, "min_score": 0.5, "no_recall_reply_customize_prompt": "抱歉，您的问题超出了我的知识范围，并且无法在当前阶段回答", "no_recall_reply_mode": 0, "show_source": false, "show_source_mode": 0, "top_k": 3, "use_rerank": true, "use_rewrite": true, "use_nl2_sql": true, "search_mode": 0}}}, "settingOnError": {"processType": 1, "timeoutMs": 180000, "retryTimes": 0}}, "outputs": [{"type": "string", "name": "output"}], "version": "3"}}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "132728"}, {"sourceNodeID": "132728", "targetNodeID": "900001"}], "versions": {"loop": "v2"}}