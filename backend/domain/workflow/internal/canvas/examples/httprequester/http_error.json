{"nodes": [{"id": "100001", "type": "1", "meta": {"position": {"x": -436.21541010770505, "y": -176.54275062137532}}, "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"type": "string", "name": "v1", "required": true}, {"type": "string", "name": "v2", "required": true}, {"type": "string", "name": "h_v1", "required": true}, {"type": "string", "name": "h_v2", "required": true}, {"type": "string", "name": "auth_key", "required": true}, {"type": "string", "name": "auth_value", "required": true}], "trigger_parameters": [{"type": "string", "name": "v1", "required": true}, {"type": "string", "name": "v2", "required": true}, {"type": "string", "name": "h_v1", "required": true}, {"type": "string", "name": "h_v2", "required": true}, {"type": "string", "name": "auth_key", "required": true}, {"type": "string", "name": "auth_value", "required": true}]}}, {"id": "900001", "type": "2", "meta": {"position": {"x": 586.7821043910523, "y": -147.81938690969343}}, "data": {"nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}, "inputs": {"terminatePlan": "returnVariables", "inputParameters": [{"name": "body", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "117004", "name": "body"}, "rawMeta": {"type": 1}}}}, {"name": "h2_v2", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "h_v2"}, "rawMeta": {"type": 1}}}}]}}}, {"id": "117004", "type": "45", "meta": {"position": {"x": 51.3169014084507, "y": -250.01391880695945}}, "data": {"nodeMeta": {"description": "用于发送API请求，从接口返回数据", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-HTTP.png", "mainColor": "#3071F2", "subTitle": "HTTP 请求", "title": "HTTP 请求"}, "inputParameters": [], "inputs": {"apiInfo": {"method": "POST", "url": "http://127.0.0.1:8080/http_error"}, "auth": {"authData": {"bearerTokenData": [{"name": "token", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "auth_key"}, "rawMeta": {"type": 1}}}, "type": "string"}], "customData": {"addTo": "query", "data": [{"name": "Key", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "auth_key"}, "rawMeta": {"type": 1}}}, "type": "string"}, {"name": "Value", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "auth_value"}, "rawMeta": {"type": 1}}}, "type": "string"}]}}, "authOpen": true, "authType": "CUSTOM_AUTH"}, "body": {"bodyData": {"binary": {"fileURL": {"type": "string", "assistType": 1, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "file"}, "rawMeta": {"type": 8}}}}, "formData": {"data": [{"name": "key_v1", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "file"}, "rawMeta": {"type": 1}}}}, {"name": "file_v1", "input": {"type": "string", "assistType": 1, "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "form_url_v2"}, "rawMeta": {"type": 8}}}}], "typeMapping": "{\"key_v1\":{\"basicType\":\"string\"},\"file_v1\":{\"basicType\":\"string\"}}"}, "formURLEncoded": [{"name": "v1", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "file"}, "rawMeta": {"type": 1}}}}, {"name": "v2", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "form_url_v2"}, "rawMeta": {"type": 1}}}}], "json": "{\n  \"v1\": \"1\",\n  \"v2\": \"{{block_output_100001.json_key}}\"\n}", "rawText": "{{block_output_100001.input}}"}, "bodyType": "EMPTY"}, "headers": [{"name": "h1", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "h_v1"}, "rawMeta": {"type": 1}}}}, {"name": "h2", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "h_v2"}, "rawMeta": {"type": 1}}}}], "params": [{"name": "query_v1", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "v1"}, "rawMeta": {"type": 1}}}}, {"name": "query_v2", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "v2"}, "rawMeta": {"type": 1}}}}], "setting": {"retryTimes": 3, "timeout": 120}, "settingOnError": {"switch": true, "dataOnErr": "{\n    \"body\": \"v1\",\n    \"statusCode\": 400,\n    \"headers\": \"error_header\"\n}"}}, "outputs": [{"type": "string", "name": "body"}, {"type": "integer", "name": "statusCode"}, {"type": "string", "name": "headers"}, {"type": "object", "name": "errorBody", "schema": [{"type": "string", "name": "errorMessage", "readonly": true}, {"type": "string", "name": "errorCode", "readonly": true}], "readonly": true}], "settingOnError": {"settingOnErrorIsOpen": true, "settingOnErrorJSON": "{\n    \"body\": \"v1\",\n    \"statusCode\": 400,\n    \"headers\": \"error_header\"\n}"}}}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "117004"}, {"sourceNodeID": "117004", "targetNodeID": "900001"}], "versions": {"loop": "v2", "batch": "v2"}}