{"nodes": [{"blocks": [], "data": {"nodeMeta": {"title": "entry"}, "outputs": [{"name": "input", "required": false, "type": "string"}], "trigger_parameters": []}, "edges": null, "id": "100001", "meta": {"position": {"x": 0, "y": 0}}, "type": "1"}, {"blocks": [], "data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "128198", "name": "output", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "output"}], "terminatePlan": "returnVariables"}, "nodeMeta": {"title": "exit"}}, "edges": null, "id": "900001", "meta": {"position": {"x": 1000, "y": 0}}, "type": "2"}, {"blocks": [], "data": {"inputs": {"fcParam": {"workflowFCParam": {"workflowList": [{"is_draft": false, "plugin_id": "7502015065143574543", "plugin_version": "", "workflow_id": "7492075279843737651", "workflow_version": "v0.0.1"}]}}, "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "input", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "input"}], "llmParam": [{"input": {"type": "integer", "value": {"content": "1706077826", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "doubao·function_call", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "modleName"}, {"input": {"type": "string", "value": {"content": "balance", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "float", "value": {"content": "1", "rawMeta": {"type": 4}, "type": "literal"}}, "name": "temperature"}, {"input": {"type": "float", "value": {"content": "0.7", "rawMeta": {"type": 4}, "type": "literal"}}, "name": "topP"}, {"input": {"type": "integer", "value": {"content": "2", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "integer", "value": {"content": "1024", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "string", "value": {"content": "{{input}}", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "rawMeta": {"type": 3}, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "use tool whenever possible", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "systemPrompt"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 180000}}, "nodeMeta": {"title": "llm"}, "outputs": [{"name": "output", "type": "string"}], "version": "3"}, "edges": null, "id": "128198", "meta": {"position": {"x": 513, "y": -63.5}}, "type": "3"}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "128198", "sourcePortID": ""}, {"sourceNodeID": "128198", "targetNodeID": "900001", "sourcePortID": ""}], "versions": {"loop": "v2"}}