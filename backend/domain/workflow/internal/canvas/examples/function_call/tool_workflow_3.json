{"nodes": [{"blocks": [], "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"name": "USER_INPUT", "required": false, "type": "string"}, {"defaultValue": "<PERSON><PERSON><PERSON>", "description": "本次请求绑定的会话，会自动写入消息、会从该会话读对话历史。", "name": "CONVERSATION_NAME", "required": false, "type": "string"}, {"name": "input", "required": false, "type": "string"}, {"name": "options", "required": false, "schema": {"type": "string"}, "type": "list"}], "settings": null, "trigger_parameters": [{"name": "USER_INPUT", "required": false, "type": "string"}, {"description": "本次请求绑定的会话，会自动写入消息、会从该会话读对话历史。", "name": "CONVERSATION_NAME", "required": false, "type": "string"}, {"name": "input", "required": false, "type": "string"}, {"name": "options", "required": false, "schema": {"type": "string"}, "type": "list"}], "version": ""}, "edges": null, "id": "100001", "meta": {"position": {"x": 1, "y": 1.4210854715202004e-14}}, "type": "1"}, {"blocks": [], "data": {"inputs": {"content": {"type": "string", "value": {"content": "the name is {{name}}, age is {{age}}", "type": "literal"}}, "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "135279", "name": "USER_RESPONSE", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "USER_RESPONSE"}, {"input": {"type": "string", "value": {"content": {"blockID": "135279", "name": "name", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "name"}, {"input": {"type": "integer", "value": {"content": {"blockID": "135279", "name": "age", "source": "block-output"}, "rawMeta": {"type": 2}, "type": "ref"}}, "name": "age"}], "streamingOutput": true, "terminatePlan": "useAnswerContent"}, "nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}}, "edges": null, "id": "900001", "meta": {"position": {"x": 1000, "y": -12.999999999999986}}, "type": "2"}, {"blocks": [], "data": {"inputs": {"answer_type": "text", "dynamic_option": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "100001", "name": "options", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "extra_output": true, "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "input", "source": "block-output"}, "type": "ref"}}, "name": "input"}], "limit": 3, "llmParam": {"generationDiversity": "default_val", "maxTokens": 1024, "modelName": "豆包·工具调用", "modelType": 1706077826, "responseFormat": 2, "systemPrompt": "be helpful and kind {{input}}", "temperature": 1, "topP": 0.7}, "option_type": "dynamic", "options": [{"name": "北京"}, {"name": "上海"}], "question": "{{input}}"}, "nodeMeta": {"description": "支持中间向用户提问问题,支持预置选项提问和开放式问题提问两种方式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Direct-Question-v2.jpg", "mainColor": "#3071F2", "subTitle": "问答", "title": "问答"}, "outputs": [{"description": "用户本轮对话输入内容", "name": "USER_RESPONSE", "required": true, "type": "string"}, {"name": "name", "required": true, "type": "string"}, {"name": "age", "required": true, "type": "integer"}]}, "edges": null, "id": "135279", "meta": {"position": {"x": 448.2344262295082, "y": -51.65409836065572}}, "type": "18"}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "135279", "sourcePortID": ""}, {"sourceNodeID": "135279", "targetNodeID": "900001", "sourcePortID": ""}], "versions": {"loop": "v2"}}