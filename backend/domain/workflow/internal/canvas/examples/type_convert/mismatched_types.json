{"nodes": [{"blocks": [], "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"name": "input", "required": false, "type": "string"}, {"name": "int_a", "required": false, "type": "integer"}, {"name": "obj_str", "required": false, "type": "string"}, {"name": "arr_str", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "num_a", "required": false, "type": "float"}, {"name": "bool_a", "required": false, "type": "boolean"}, {"name": "obj", "required": false, "schema": [], "type": "object"}], "trigger_parameters": [{"name": "input", "required": false, "type": "string"}, {"name": "int_a", "required": false, "type": "integer"}, {"name": "obj_str", "required": false, "type": "string"}, {"name": "arr_str", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "num_a", "required": false, "type": "float"}, {"name": "bool_a", "required": false, "type": "boolean"}, {"name": "obj", "required": false, "schema": [], "type": "object"}]}, "edges": null, "id": "100001", "meta": {"position": {"x": 266.9530967429301, "y": -668.9813153618387}}, "type": "1"}, {"blocks": [], "data": {"inputs": {"content": {"type": "string", "value": {"content": "{{int_a}} {{input}} {{obj_str}} {{arr_str}} {{num_a}} {{bool_a}} {{obj}}\n{{output1}} {{arr_str[0]}} {{obj_str.s[1]}}", "type": "literal"}}, "inputParameters": [{"input": {"type": "boolean", "value": {"content": {"blockID": "100001", "name": "int_a", "source": "block-output"}, "rawMeta": {"type": 3}, "type": "ref"}}, "name": "int_a"}, {"input": {"type": "string", "value": {"content": {"blockID": "172190", "name": "output", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "output1"}, {"input": {"schema": {"type": "integer"}, "type": "list", "value": {"content": {"blockID": "100001", "name": "input", "source": "block-output"}, "rawMeta": {"type": 100}, "type": "ref"}}, "name": "input"}, {"input": {"type": "object", "value": {"content": {"blockID": "100001", "name": "obj_str", "source": "block-output"}, "rawMeta": {"type": 6}, "type": "ref"}}, "name": "obj_str"}, {"input": {"schema": {"type": "object"}, "type": "list", "value": {"content": {"blockID": "100001", "name": "arr_str", "source": "block-output"}, "rawMeta": {"type": 103}, "type": "ref"}}, "name": "arr_str"}, {"input": {"type": "integer", "value": {"content": {"blockID": "100001", "name": "num_a", "source": "block-output"}, "rawMeta": {"type": 2}, "type": "ref"}}, "name": "num_a"}, {"input": {"type": "float", "value": {"content": {"blockID": "100001", "name": "bool_a", "source": "block-output"}, "rawMeta": {"type": 4}, "type": "ref"}}, "name": "bool_a"}, {"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "obj", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "obj"}], "streamingOutput": true, "terminatePlan": "useAnswerContent"}, "nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}}, "edges": null, "id": "900001", "meta": {"position": {"x": 276.0536196376395, "y": -222.16519220364182}}, "type": "2"}, {"blocks": [], "data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "input", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "input"}], "llmParam": [{"input": {"type": "integer", "value": {"content": "1737521813", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "豆包·1.5·Pro·32k", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "modleName"}, {"input": {"type": "string", "value": {"content": "balance", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "float", "value": {"content": "0.8", "rawMeta": {"type": 4}, "type": "literal"}}, "name": "temperature"}, {"input": {"type": "integer", "value": {"content": "4096", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "integer", "value": {"content": "2", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "string", "value": {"content": "{{input}}", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "rawMeta": {"type": 3}, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "systemPrompt"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 180000}}, "nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "mainColor": "#5C62FF", "subTitle": "大模型", "title": "大模型"}, "outputs": [{"name": "output", "type": "string"}], "version": "3"}, "edges": null, "id": "172190", "meta": {"position": {"x": 276.0536196376395, "y": -510.12080631587185}}, "type": "3"}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "172190", "sourcePortID": ""}, {"sourceNodeID": "100001", "targetNodeID": "900001", "sourcePortID": ""}, {"sourceNodeID": "172190", "targetNodeID": "900001", "sourcePortID": ""}], "versions": {"loop": "v2"}}