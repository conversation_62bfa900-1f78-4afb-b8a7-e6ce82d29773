// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "github.com/coze-dev/coze-studio/backend/api/model/crossdomain/agentrun"

const TableNameRunRecord = "run_record"

// RunRecord run record
type RunRecord struct {
	ID             int64           `gorm:"column:id;primaryKey;comment:id" json:"id"`                                                                                                         // id
	ConversationID int64           `gorm:"column:conversation_id;not null;comment:conversation id" json:"conversation_id"`                                                                    // conversation id
	SectionID      int64           `gorm:"column:section_id;not null;comment:section ID" json:"section_id"`                                                                                   // section ID
	AgentID        int64           `gorm:"column:agent_id;not null;comment:agent_id" json:"agent_id"`                                                                                         // agent_id
	UserID         string          `gorm:"column:user_id;not null;comment:user id" json:"user_id"`                                                                                            // user id
	Source         int32           `gorm:"column:source;not null;comment:Execute source 0 API" json:"source"`                                                                                 // Execute source 0 API
	Status         string          `gorm:"column:status;not null;comment:status,0 Unknown, 1-Created,2-InProgress,3-Completed,4-Failed,5-Expired,6-Cancelled,7-RequiresAction" json:"status"` // status,0 Unknown, 1-Created,2-InProgress,3-Completed,4-Failed,5-Expired,6-Cancelled,7-RequiresAction
	CreatorID      int64           `gorm:"column:creator_id;not null;comment:creator id" json:"creator_id"`                                                                                   // creator id
	CreatedAt      int64           `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"`                                             // Create Time in Milliseconds
	UpdatedAt      int64           `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"`                                             // Update Time in Milliseconds
	FailedAt       int64           `gorm:"column:failed_at;not null;comment:Fail Time in Milliseconds" json:"failed_at"`                                                                      // Fail Time in Milliseconds
	LastError      string          `gorm:"column:last_error;comment:error message" json:"last_error"`                                                                                         // error message
	CompletedAt    int64           `gorm:"column:completed_at;not null;comment:Finish Time in Milliseconds" json:"completed_at"`                                                              // Finish Time in Milliseconds
	ChatRequest    string          `gorm:"column:chat_request;comment:Original request field" json:"chat_request"`                                                                            // Original request field
	Ext            string          `gorm:"column:ext;comment:ext" json:"ext"`                                                                                                                 // ext
	Usage          *agentrun.Usage `gorm:"column:usage;comment:usage;serializer:json" json:"usage"`                                                                                           // usage
}

// TableName RunRecord's table name
func (*RunRecord) TableName() string {
	return TableNameRunRecord
}
