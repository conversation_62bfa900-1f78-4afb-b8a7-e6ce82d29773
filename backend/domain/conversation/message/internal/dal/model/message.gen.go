// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMessage = "message"

// Message message record
type Message struct {
	ID               int64  `gorm:"column:id;primaryKey;autoIncrement:true;comment:id" json:"id"`                                                                          // id
	RunID            int64  `gorm:"column:run_id;not null;comment:run_id" json:"run_id"`                                                                                   // run_id
	ConversationID   int64  `gorm:"column:conversation_id;not null;comment:conversation id" json:"conversation_id"`                                                        // conversation id
	UserID           string `gorm:"column:user_id;not null;comment:user id" json:"user_id"`                                                                                // user id
	AgentID          int64  `gorm:"column:agent_id;not null;comment:agent_id" json:"agent_id"`                                                                             // agent_id
	Role             string `gorm:"column:role;not null;comment:role: user、assistant、system" json:"role"`                                                                  // role: user、assistant、system
	ContentType      string `gorm:"column:content_type;not null;comment:content type 1 text" json:"content_type"`                                                          // content type 1 text
	Content          string `gorm:"column:content;comment:content" json:"content"`                                                                                         // content
	MessageType      string `gorm:"column:message_type;not null;comment:message_type" json:"message_type"`                                                                 // message_type
	DisplayContent   string `gorm:"column:display_content;comment:display content" json:"display_content"`                                                                 // display content
	Ext              string `gorm:"column:ext;comment:message ext" json:"ext"`                                                                                             // message ext
	SectionID        int64  `gorm:"column:section_id;comment:section_id" json:"section_id"`                                                                                // section_id
	BrokenPosition   int32  `gorm:"column:broken_position;default:-1;comment:broken position" json:"broken_position"`                                                      // broken position
	Status           int32  `gorm:"column:status;not null;comment:message status: 1 Available 2 Deleted 3 Replaced 4 Broken 5 Failed 6 Streaming 7 Pending" json:"status"` // message status: 1 Available 2 Deleted 3 Replaced 4 Broken 5 Failed 6 Streaming 7 Pending
	ModelContent     string `gorm:"column:model_content;comment:model content" json:"model_content"`                                                                       // model content
	MetaInfo         string `gorm:"column:meta_info;comment:text tagging information such as citation and highlighting" json:"meta_info"`                                  // text tagging information such as citation and highlighting
	ReasoningContent string `gorm:"column:reasoning_content;comment:reasoning content" json:"reasoning_content"`                                                           // reasoning content
	CreatedAt        int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"`                                 // Create Time in Milliseconds
	UpdatedAt        int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"`                                 // Update Time in Milliseconds
}

// TableName Message's table name
func (*Message) TableName() string {
	return TableNameMessage
}
