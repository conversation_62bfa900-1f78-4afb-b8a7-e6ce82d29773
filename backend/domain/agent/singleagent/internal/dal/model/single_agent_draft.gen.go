// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"github.com/coze-dev/coze-studio/backend/api/model/ocean/cloud/bot_common"
	"gorm.io/gorm"
)

const TableNameSingleAgentDraft = "single_agent_draft"

// SingleAgentDraft Single Agent Draft Copy Table
type SingleAgentDraft struct {
	ID                      int64                             `gorm:"column:id;primaryKey;autoIncrement:true;comment:Primary Key ID" json:"id"`                                     // Primary Key ID
	AgentID                 int64                             `gorm:"column:agent_id;not null;comment:Agent ID" json:"agent_id"`                                                    // Agent ID
	CreatorID               int64                             `gorm:"column:creator_id;not null;comment:Creator ID" json:"creator_id"`                                              // Creator ID
	SpaceID                 int64                             `gorm:"column:space_id;not null;comment:Space ID" json:"space_id"`                                                    // Space ID
	Name                    string                            `gorm:"column:name;not null;comment:Agent Name" json:"name"`                                                          // Agent Name
	Description             string                            `gorm:"column:description;not null;comment:Agent Description" json:"description"`                                     // Agent Description
	IconURI                 string                            `gorm:"column:icon_uri;not null;comment:Icon URI" json:"icon_uri"`                                                    // Icon URI
	CreatedAt               int64                             `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"`        // Create Time in Milliseconds
	UpdatedAt               int64                             `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"`        // Update Time in Milliseconds
	DeletedAt               gorm.DeletedAt                    `gorm:"column:deleted_at;comment:delete time in millisecond" json:"deleted_at"`                                       // delete time in millisecond
	VariablesMetaID         *int64                            `gorm:"column:variables_meta_id;comment:variables meta table ID" json:"variables_meta_id"`                            // variables meta table ID
	ModelInfo               *bot_common.ModelInfo             `gorm:"column:model_info;comment:Model Configuration Information;serializer:json" json:"model_info"`                  // Model Configuration Information
	OnboardingInfo          *bot_common.OnboardingInfo        `gorm:"column:onboarding_info;comment:Onboarding Information;serializer:json" json:"onboarding_info"`                 // Onboarding Information
	Prompt                  *bot_common.PromptInfo            `gorm:"column:prompt;comment:Agent Prompt Configuration;serializer:json" json:"prompt"`                               // Agent Prompt Configuration
	Plugin                  []*bot_common.PluginInfo          `gorm:"column:plugin;comment:Agent Plugin Base Configuration;serializer:json" json:"plugin"`                          // Agent Plugin Base Configuration
	Knowledge               *bot_common.Knowledge             `gorm:"column:knowledge;comment:Agent Knowledge Base Configuration;serializer:json" json:"knowledge"`                 // Agent Knowledge Base Configuration
	Workflow                []*bot_common.WorkflowInfo        `gorm:"column:workflow;comment:Agent Workflow Configuration;serializer:json" json:"workflow"`                         // Agent Workflow Configuration
	SuggestReply            *bot_common.SuggestReplyInfo      `gorm:"column:suggest_reply;comment:Suggested Replies;serializer:json" json:"suggest_reply"`                          // Suggested Replies
	JumpConfig              *bot_common.JumpConfig            `gorm:"column:jump_config;comment:Jump Configuration;serializer:json" json:"jump_config"`                             // Jump Configuration
	BackgroundImageInfoList []*bot_common.BackgroundImageInfo `gorm:"column:background_image_info_list;comment:Background image;serializer:json" json:"background_image_info_list"` // Background image
	DatabaseConfig          []*bot_common.Database            `gorm:"column:database_config;comment:Agent Database Base Configuration;serializer:json" json:"database_config"`      // Agent Database Base Configuration
	ShortcutCommand         []string                          `gorm:"column:shortcut_command;comment:shortcut command;serializer:json" json:"shortcut_command"`                     // shortcut command
}

// TableName SingleAgentDraft's table name
func (*SingleAgentDraft) TableName() string {
	return TableNameSingleAgentDraft
}
