// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/agent/singleagent/internal/dal/model"
)

func newSingleAgentDraft(db *gorm.DB, opts ...gen.DOOption) singleAgentDraft {
	_singleAgentDraft := singleAgentDraft{}

	_singleAgentDraft.singleAgentDraftDo.UseDB(db, opts...)
	_singleAgentDraft.singleAgentDraftDo.UseModel(&model.SingleAgentDraft{})

	tableName := _singleAgentDraft.singleAgentDraftDo.TableName()
	_singleAgentDraft.ALL = field.NewAsterisk(tableName)
	_singleAgentDraft.ID = field.NewInt64(tableName, "id")
	_singleAgentDraft.AgentID = field.NewInt64(tableName, "agent_id")
	_singleAgentDraft.CreatorID = field.NewInt64(tableName, "creator_id")
	_singleAgentDraft.SpaceID = field.NewInt64(tableName, "space_id")
	_singleAgentDraft.Name = field.NewString(tableName, "name")
	_singleAgentDraft.Description = field.NewString(tableName, "description")
	_singleAgentDraft.IconURI = field.NewString(tableName, "icon_uri")
	_singleAgentDraft.CreatedAt = field.NewInt64(tableName, "created_at")
	_singleAgentDraft.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_singleAgentDraft.DeletedAt = field.NewField(tableName, "deleted_at")
	_singleAgentDraft.VariablesMetaID = field.NewInt64(tableName, "variables_meta_id")
	_singleAgentDraft.ModelInfo = field.NewField(tableName, "model_info")
	_singleAgentDraft.OnboardingInfo = field.NewField(tableName, "onboarding_info")
	_singleAgentDraft.Prompt = field.NewField(tableName, "prompt")
	_singleAgentDraft.Plugin = field.NewField(tableName, "plugin")
	_singleAgentDraft.Knowledge = field.NewField(tableName, "knowledge")
	_singleAgentDraft.Workflow = field.NewField(tableName, "workflow")
	_singleAgentDraft.SuggestReply = field.NewField(tableName, "suggest_reply")
	_singleAgentDraft.JumpConfig = field.NewField(tableName, "jump_config")
	_singleAgentDraft.BackgroundImageInfoList = field.NewField(tableName, "background_image_info_list")
	_singleAgentDraft.DatabaseConfig = field.NewField(tableName, "database_config")
	_singleAgentDraft.ShortcutCommand = field.NewField(tableName, "shortcut_command")

	_singleAgentDraft.fillFieldMap()

	return _singleAgentDraft
}

// singleAgentDraft Single Agent Draft Copy Table
type singleAgentDraft struct {
	singleAgentDraftDo

	ALL                     field.Asterisk
	ID                      field.Int64  // Primary Key ID
	AgentID                 field.Int64  // Agent ID
	CreatorID               field.Int64  // Creator ID
	SpaceID                 field.Int64  // Space ID
	Name                    field.String // Agent Name
	Description             field.String // Agent Description
	IconURI                 field.String // Icon URI
	CreatedAt               field.Int64  // Create Time in Milliseconds
	UpdatedAt               field.Int64  // Update Time in Milliseconds
	DeletedAt               field.Field  // delete time in millisecond
	VariablesMetaID         field.Int64  // variables meta table ID
	ModelInfo               field.Field  // Model Configuration Information
	OnboardingInfo          field.Field  // Onboarding Information
	Prompt                  field.Field  // Agent Prompt Configuration
	Plugin                  field.Field  // Agent Plugin Base Configuration
	Knowledge               field.Field  // Agent Knowledge Base Configuration
	Workflow                field.Field  // Agent Workflow Configuration
	SuggestReply            field.Field  // Suggested Replies
	JumpConfig              field.Field  // Jump Configuration
	BackgroundImageInfoList field.Field  // Background image
	DatabaseConfig          field.Field  // Agent Database Base Configuration
	ShortcutCommand         field.Field  // shortcut command

	fieldMap map[string]field.Expr
}

func (s singleAgentDraft) Table(newTableName string) *singleAgentDraft {
	s.singleAgentDraftDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s singleAgentDraft) As(alias string) *singleAgentDraft {
	s.singleAgentDraftDo.DO = *(s.singleAgentDraftDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *singleAgentDraft) updateTableName(table string) *singleAgentDraft {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.AgentID = field.NewInt64(table, "agent_id")
	s.CreatorID = field.NewInt64(table, "creator_id")
	s.SpaceID = field.NewInt64(table, "space_id")
	s.Name = field.NewString(table, "name")
	s.Description = field.NewString(table, "description")
	s.IconURI = field.NewString(table, "icon_uri")
	s.CreatedAt = field.NewInt64(table, "created_at")
	s.UpdatedAt = field.NewInt64(table, "updated_at")
	s.DeletedAt = field.NewField(table, "deleted_at")
	s.VariablesMetaID = field.NewInt64(table, "variables_meta_id")
	s.ModelInfo = field.NewField(table, "model_info")
	s.OnboardingInfo = field.NewField(table, "onboarding_info")
	s.Prompt = field.NewField(table, "prompt")
	s.Plugin = field.NewField(table, "plugin")
	s.Knowledge = field.NewField(table, "knowledge")
	s.Workflow = field.NewField(table, "workflow")
	s.SuggestReply = field.NewField(table, "suggest_reply")
	s.JumpConfig = field.NewField(table, "jump_config")
	s.BackgroundImageInfoList = field.NewField(table, "background_image_info_list")
	s.DatabaseConfig = field.NewField(table, "database_config")
	s.ShortcutCommand = field.NewField(table, "shortcut_command")

	s.fillFieldMap()

	return s
}

func (s *singleAgentDraft) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *singleAgentDraft) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 22)
	s.fieldMap["id"] = s.ID
	s.fieldMap["agent_id"] = s.AgentID
	s.fieldMap["creator_id"] = s.CreatorID
	s.fieldMap["space_id"] = s.SpaceID
	s.fieldMap["name"] = s.Name
	s.fieldMap["description"] = s.Description
	s.fieldMap["icon_uri"] = s.IconURI
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["deleted_at"] = s.DeletedAt
	s.fieldMap["variables_meta_id"] = s.VariablesMetaID
	s.fieldMap["model_info"] = s.ModelInfo
	s.fieldMap["onboarding_info"] = s.OnboardingInfo
	s.fieldMap["prompt"] = s.Prompt
	s.fieldMap["plugin"] = s.Plugin
	s.fieldMap["knowledge"] = s.Knowledge
	s.fieldMap["workflow"] = s.Workflow
	s.fieldMap["suggest_reply"] = s.SuggestReply
	s.fieldMap["jump_config"] = s.JumpConfig
	s.fieldMap["background_image_info_list"] = s.BackgroundImageInfoList
	s.fieldMap["database_config"] = s.DatabaseConfig
	s.fieldMap["shortcut_command"] = s.ShortcutCommand
}

func (s singleAgentDraft) clone(db *gorm.DB) singleAgentDraft {
	s.singleAgentDraftDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s singleAgentDraft) replaceDB(db *gorm.DB) singleAgentDraft {
	s.singleAgentDraftDo.ReplaceDB(db)
	return s
}

type singleAgentDraftDo struct{ gen.DO }

type ISingleAgentDraftDo interface {
	gen.SubQuery
	Debug() ISingleAgentDraftDo
	WithContext(ctx context.Context) ISingleAgentDraftDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISingleAgentDraftDo
	WriteDB() ISingleAgentDraftDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISingleAgentDraftDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISingleAgentDraftDo
	Not(conds ...gen.Condition) ISingleAgentDraftDo
	Or(conds ...gen.Condition) ISingleAgentDraftDo
	Select(conds ...field.Expr) ISingleAgentDraftDo
	Where(conds ...gen.Condition) ISingleAgentDraftDo
	Order(conds ...field.Expr) ISingleAgentDraftDo
	Distinct(cols ...field.Expr) ISingleAgentDraftDo
	Omit(cols ...field.Expr) ISingleAgentDraftDo
	Join(table schema.Tabler, on ...field.Expr) ISingleAgentDraftDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISingleAgentDraftDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISingleAgentDraftDo
	Group(cols ...field.Expr) ISingleAgentDraftDo
	Having(conds ...gen.Condition) ISingleAgentDraftDo
	Limit(limit int) ISingleAgentDraftDo
	Offset(offset int) ISingleAgentDraftDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISingleAgentDraftDo
	Unscoped() ISingleAgentDraftDo
	Create(values ...*model.SingleAgentDraft) error
	CreateInBatches(values []*model.SingleAgentDraft, batchSize int) error
	Save(values ...*model.SingleAgentDraft) error
	First() (*model.SingleAgentDraft, error)
	Take() (*model.SingleAgentDraft, error)
	Last() (*model.SingleAgentDraft, error)
	Find() ([]*model.SingleAgentDraft, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SingleAgentDraft, err error)
	FindInBatches(result *[]*model.SingleAgentDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SingleAgentDraft) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISingleAgentDraftDo
	Assign(attrs ...field.AssignExpr) ISingleAgentDraftDo
	Joins(fields ...field.RelationField) ISingleAgentDraftDo
	Preload(fields ...field.RelationField) ISingleAgentDraftDo
	FirstOrInit() (*model.SingleAgentDraft, error)
	FirstOrCreate() (*model.SingleAgentDraft, error)
	FindByPage(offset int, limit int) (result []*model.SingleAgentDraft, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISingleAgentDraftDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s singleAgentDraftDo) Debug() ISingleAgentDraftDo {
	return s.withDO(s.DO.Debug())
}

func (s singleAgentDraftDo) WithContext(ctx context.Context) ISingleAgentDraftDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s singleAgentDraftDo) ReadDB() ISingleAgentDraftDo {
	return s.Clauses(dbresolver.Read)
}

func (s singleAgentDraftDo) WriteDB() ISingleAgentDraftDo {
	return s.Clauses(dbresolver.Write)
}

func (s singleAgentDraftDo) Session(config *gorm.Session) ISingleAgentDraftDo {
	return s.withDO(s.DO.Session(config))
}

func (s singleAgentDraftDo) Clauses(conds ...clause.Expression) ISingleAgentDraftDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s singleAgentDraftDo) Returning(value interface{}, columns ...string) ISingleAgentDraftDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s singleAgentDraftDo) Not(conds ...gen.Condition) ISingleAgentDraftDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s singleAgentDraftDo) Or(conds ...gen.Condition) ISingleAgentDraftDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s singleAgentDraftDo) Select(conds ...field.Expr) ISingleAgentDraftDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s singleAgentDraftDo) Where(conds ...gen.Condition) ISingleAgentDraftDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s singleAgentDraftDo) Order(conds ...field.Expr) ISingleAgentDraftDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s singleAgentDraftDo) Distinct(cols ...field.Expr) ISingleAgentDraftDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s singleAgentDraftDo) Omit(cols ...field.Expr) ISingleAgentDraftDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s singleAgentDraftDo) Join(table schema.Tabler, on ...field.Expr) ISingleAgentDraftDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s singleAgentDraftDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISingleAgentDraftDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s singleAgentDraftDo) RightJoin(table schema.Tabler, on ...field.Expr) ISingleAgentDraftDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s singleAgentDraftDo) Group(cols ...field.Expr) ISingleAgentDraftDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s singleAgentDraftDo) Having(conds ...gen.Condition) ISingleAgentDraftDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s singleAgentDraftDo) Limit(limit int) ISingleAgentDraftDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s singleAgentDraftDo) Offset(offset int) ISingleAgentDraftDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s singleAgentDraftDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISingleAgentDraftDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s singleAgentDraftDo) Unscoped() ISingleAgentDraftDo {
	return s.withDO(s.DO.Unscoped())
}

func (s singleAgentDraftDo) Create(values ...*model.SingleAgentDraft) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s singleAgentDraftDo) CreateInBatches(values []*model.SingleAgentDraft, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s singleAgentDraftDo) Save(values ...*model.SingleAgentDraft) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s singleAgentDraftDo) First() (*model.SingleAgentDraft, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentDraft), nil
	}
}

func (s singleAgentDraftDo) Take() (*model.SingleAgentDraft, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentDraft), nil
	}
}

func (s singleAgentDraftDo) Last() (*model.SingleAgentDraft, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentDraft), nil
	}
}

func (s singleAgentDraftDo) Find() ([]*model.SingleAgentDraft, error) {
	result, err := s.DO.Find()
	return result.([]*model.SingleAgentDraft), err
}

func (s singleAgentDraftDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SingleAgentDraft, err error) {
	buf := make([]*model.SingleAgentDraft, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s singleAgentDraftDo) FindInBatches(result *[]*model.SingleAgentDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s singleAgentDraftDo) Attrs(attrs ...field.AssignExpr) ISingleAgentDraftDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s singleAgentDraftDo) Assign(attrs ...field.AssignExpr) ISingleAgentDraftDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s singleAgentDraftDo) Joins(fields ...field.RelationField) ISingleAgentDraftDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s singleAgentDraftDo) Preload(fields ...field.RelationField) ISingleAgentDraftDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s singleAgentDraftDo) FirstOrInit() (*model.SingleAgentDraft, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentDraft), nil
	}
}

func (s singleAgentDraftDo) FirstOrCreate() (*model.SingleAgentDraft, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentDraft), nil
	}
}

func (s singleAgentDraftDo) FindByPage(offset int, limit int) (result []*model.SingleAgentDraft, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s singleAgentDraftDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s singleAgentDraftDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s singleAgentDraftDo) Delete(models ...*model.SingleAgentDraft) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *singleAgentDraftDo) withDO(do gen.Dao) *singleAgentDraftDo {
	s.DO = *do.(*gen.DO)
	return s
}
