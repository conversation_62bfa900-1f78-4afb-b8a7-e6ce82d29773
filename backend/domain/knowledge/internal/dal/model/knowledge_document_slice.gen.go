// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameKnowledgeDocumentSlice = "knowledge_document_slice"

// KnowledgeDocumentSlice knowledge document slice
type KnowledgeDocumentSlice struct {
	ID          int64          `gorm:"column:id;primaryKey;comment:id" json:"id"`                                               // id
	KnowledgeID int64          `gorm:"column:knowledge_id;not null;comment:knowledge id" json:"knowledge_id"`                   // knowledge id
	DocumentID  int64          `gorm:"column:document_id;not null;comment:document_id" json:"document_id"`                      // document_id
	Content     string         `gorm:"column:content;comment:content" json:"content"`                                           // content
	Sequence    float64        `gorm:"column:sequence;not null;comment:slice sequence number, starting from 1" json:"sequence"` // slice sequence number, starting from 1
	CreatedAt   int64          `gorm:"column:created_at;not null;comment:Create Time in Milliseconds" json:"created_at"`        // Create Time in Milliseconds
	UpdatedAt   int64          `gorm:"column:updated_at;not null;comment:Update Time in Milliseconds" json:"updated_at"`        // Update Time in Milliseconds
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;comment:Delete Time" json:"deleted_at"`                                 // Delete Time
	CreatorID   int64          `gorm:"column:creator_id;not null;comment:creator id" json:"creator_id"`                         // creator id
	SpaceID     int64          `gorm:"column:space_id;not null;comment:space id" json:"space_id"`                               // space id
	Status      int32          `gorm:"column:status;not null;comment:status" json:"status"`                                     // status
	FailReason  string         `gorm:"column:fail_reason;comment:fail reason" json:"fail_reason"`                               // fail reason
	Hit         int64          `gorm:"column:hit;not null;comment:hit counts" json:"hit"`                                       // hit counts
}

// TableName KnowledgeDocumentSlice's table name
func (*KnowledgeDocumentSlice) TableName() string {
	return TableNameKnowledgeDocumentSlice
}
